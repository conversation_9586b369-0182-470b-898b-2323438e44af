package ro.amiq.dvt.altera.autoconfig;

import ro.amiq.dvt.tcl.model.DVTCommandTypeArg;

public enum DVTQuartusCommandType implements IAlteraAutoconfigCommandType {
   SOURCE("source", DVTQuartusSourceCommand.class, 1, new DVTCommandTypeArg[0]),
   SET_GLOBAL_ASSIGNMENT("set_global_assignment", DVTQuartusSetGlobalAssignment.class, 1, new DVTCommandTypeArg[]{new DVTCommandTypeArg("name", true), new DVTCommandTypeArg("section_id", true), new DVTCommandTypeArg("comment", true), new DVTCommandTypeArg("disable", false), new DVTCommandTypeArg("entity", true), new DVTCommandTypeArg("remove", false), new DVTCommandTypeArg("tag", true), new DVTCommandTypeArg("library", true), new DVTCommandTypeArg("rise", false), new DVTCommandTypeArg("fall", false)}),
   SET_INSTANCE_ASSIGNMENT("set_instance_assignment", DVTQuartusSetInstanceAssignment.class, 1, new DVTCommandTypeArg[]{new DVTCommandTypeArg("name", true), new DVTCommandTypeArg("comment", true), new DVTCommandTypeArg("disable", false), new DVTCommandTypeArg("entity", true), new DVTCommandTypeArg("fall", false), new DVTCommandTypeArg("from", true), new DVTCommandTypeArg("remove", false), new DVTCommandTypeArg("rise", false), new DVTCommandTypeArg("section_id", true), new DVTCommandTypeArg("tag", true), new DVTCommandTypeArg("to", true), new DVTCommandTypeArg("library", true)}),
   SET_LOCATION_ASSIGNMENT("set_location_assignment", DVTQuartusSetLocationAssignment.class, 1, new DVTCommandTypeArg[]{new DVTCommandTypeArg("to", true), new DVTCommandTypeArg("comment", true), new DVTCommandTypeArg("disable", false), new DVTCommandTypeArg("remove", false), new DVTCommandTypeArg("tag", true), new DVTCommandTypeArg("library", true)});

   private DVTQuartusCommandType(String var3, Class var4, int var5, DVTCommandTypeArg... var6) {
      this.a(var3, var4, var5, var6);
   }
}
