package ro.amiq.dvt.altera.autoconfig;

import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.builders.DVTBuildConsoleRegistry;
import ro.amiq.dvt.tcl.model.DVTCommandResult;
import ro.amiq.dvt.tcl.model.DVTDebugKind;
import ro.amiq.dvt.tcl.model.DVTTclInterp;
import ro.amiq.dvt.tcl.model.DVTTclUtils;
import ro.amiq.dvt.tcl.model.IDVTCommandType;
import tcl.lang.TclException;
import tcl.lang.TclObject;

public class DVTQuartusSetGlobalAssignment extends AlteraAutoconfigCommand {
   private static final String c = "VHDL_INPUT_VERSION";
   private static final String d = "VERILOG_INPUT_VERSION";
   private static final String e = "VHDL_FILE";
   private static final String f = "SYSTEMVERILOG_FILE";
   private static final String g = "VERILOG_FILE";
   private static final String h = "QIP_FILE";
   private static final String i = "IP_FILE";
   private static final String j = "PBIP_FILE";
   private static final String k = "QSYS_FILE";
   private static final String l = "USER_LIBRARIES";
   private static final String m = "SEARCH_PATH";
   private static final String n = "TOP_LEVEL_ENTITY";
   private static final String o = "work";
   private static final String p = "library";
   private static final String q = "name";

   protected DVTQuartusSetGlobalAssignment(IDVTCommandType var1) {
      super(var1);
   }

   public void a(DVTTclInterp var1, TclObject[] var2) throws TclException {
      if (this.a != null && this.a.length != 0) {
         DVTBuildConsoleRegistry.a(this.a().b());
         DVTCommandResult var3 = this.f();
         if (var3 != null) {
            String var4 = var3.a();
            if (var4 != null) {
               StringBuilder var5 = (new StringBuilder("set_global_assignment ")).append(var3.a("name")).append(" ").append(var4).append(" ...");
               if ("TOP_LEVEL_ENTITY".equals(var3.a("name"))) {
                  DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                  this.a().e("TOP_LEVEL_ENTITY", var4);
               } else if ("SEARCH_PATH".equals(var3.a("name"))) {
                  DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                  this.a().a(Path.fromOSString(var4));
               } else if ("USER_LIBRARIES".equals(var3.a("name"))) {
                  DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                  this.a().b(Path.fromOSString(var4));
               } else if ("QIP_FILE".equals(var3.a("name"))) {
                  DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                  this.a().a(Path.fromOSString(var4), ((AlteraAutoconfigTCLInterp)var1).f());
               } else if (!"IP_FILE".equals(var3.a("name")) && !"PBIP_FILE".equals(var3.a("name")) && !"QSYS_FILE".equals(var3.a("name"))) {
                  if ("VERILOG_FILE".equals(var3.a("name"))) {
                     DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                     String var8 = this.a(var3);
                     this.a().b(var8, var4);
                  } else if ("SYSTEMVERILOG_FILE".equals(var3.a("name"))) {
                     DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                     String var9 = this.a(var3);
                     this.a().c(var9, var4);
                  } else if ("VHDL_FILE".equals(var3.a("name"))) {
                     DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                     String var10 = this.a(var3);
                     this.a().d(var10, var4);
                  } else if (!"VERILOG_INPUT_VERSION".equals(var3.a("name")) && !"VHDL_INPUT_VERSION".equals(var3.a("name"))) {
                     this.a().e(var3.a("name"), var4);
                  } else {
                     DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
                     this.a(var1, var3);
                  }
               } else {
                  IPath var6 = Path.fromOSString(var4);
                  String var7 = var6.removeFileExtension().lastSegment();
                  this.a().a(var7, ((AlteraAutoconfigTCLInterp)var1).f());
               }

            }
         }
      }
   }

   private String a(DVTCommandResult var1) {
      return var1.a("library") != null ? var1.a("library") : "work";
   }

   private void a(DVTTclInterp var1, DVTCommandResult var2) {
      if (!((AlteraAutoconfigTCLInterp)var1).f().endsWith(".qip")) {
         this.a().e(var2.a("name"), var2.a());
      }
   }
}
