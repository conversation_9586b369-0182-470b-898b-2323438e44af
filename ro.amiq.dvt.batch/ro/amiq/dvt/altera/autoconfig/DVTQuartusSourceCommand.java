package ro.amiq.dvt.altera.autoconfig;

import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.tcl.model.DVTCommandResult;
import ro.amiq.dvt.tcl.model.DVTDebugKind;
import ro.amiq.dvt.tcl.model.DVTTclInterp;
import ro.amiq.dvt.tcl.model.DVTTclUtils;
import ro.amiq.dvt.tcl.model.IDVTCommandType;
import tcl.lang.TclException;
import tcl.lang.TclObject;

public class DVTQuartusSourceCommand extends AlteraAutoconfigCommand {
   protected DVTQuartusSourceCommand(IDVTCommandType var1) {
      super(var1);
   }

   public void a(DVTTclInterp var1, TclObject[] var2) throws TclException {
      if (this.a != null && this.a.length != 0) {
         DVTCommandResult var3 = this.f();
         if (var3 != null) {
            String var4 = var3.a();
            if (var4 != null) {
               this.a().b(Path.fromOSString(var4), ((AlteraAutoconfigTCLInterp)var1).f());
               StringBuilder var5 = (new StringBuilder("source ")).append(var4).append(" ...");
               DVTTclUtils.a("[Quartus] " + var5, this.a().b(), var1.e(), DVTDebugKind.QUARTUS_DEBUG_INFO, var1.g());
            }
         }
      }
   }
}
