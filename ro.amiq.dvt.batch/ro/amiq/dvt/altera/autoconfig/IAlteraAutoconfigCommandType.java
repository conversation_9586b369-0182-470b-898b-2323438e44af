package ro.amiq.dvt.altera.autoconfig;

import ro.amiq.dvt.startup.core.DVTLogger;
import ro.amiq.dvt.tcl.model.IDVTCommandType;

public interface IAlteraAutoconfigCommandType extends IDVTCommandType {
   default AlteraAutoconfigCommand a(AlteraAutoconfigModel var1) {
      try {
         AlteraAutoconfigCommand var2 = (AlteraAutoconfigCommand)this.f();
         var2.a(var1);
         return var2;
      } catch (Exception var3) {
         DVTLogger.INSTANCE.logError((Throwable)var3);
         return null;
      }
   }
}
