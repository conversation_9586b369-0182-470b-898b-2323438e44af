package ro.amiq.dvt.altera.autoconfig;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.regex.Pattern;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.buildconfig.AutoConfig;
import ro.amiq.dvt.buildconfig.BuildConfigParser;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.builders.DVTBuildConsoleRegistry;
import ro.amiq.dvt.startup.core.DVTLogger;
import ro.amiq.dvt.tcl.model.DVTDebugKind;
import ro.amiq.dvt.tcl.model.DVTTclUtils;
import ro.amiq.dvt.utils.DVTStringUtil;

public class AlteraAutoconfigModel {
   private static final String a = System.lineSeparator();
   private static final String b = "quartus2.ini";
   private static final String c = "verilog";
   private static final String d = "vhdl";
   private static final String e = "systemverilog";
   private static final String f = "-lang vlog";
   private static final String g = "-lang vhdl";
   private static final String h = System.getenv("HOME");
   private static final String i = System.getenv("USERPROFILE");
   private static final String j = System.getenv("TMP");
   private static final String k = System.getenv("TEMP");
   private static final Pattern l = Pattern.compile("\\s+\\t*");
   private String m = "SYSTEMVERILOG_2005";
   private String n = "VHDL_1993";
   private String o = "";
   private IProject p;
   private String q;
   private String r;
   private String s;
   private BuildConfigParser.AutoConfigParameters t;
   private Map u;
   private Map v;
   private Map w;
   private List x;
   private List y;
   private Map z;
   private Map A;
   private Map B;
   private Map C;
   private Map D;
   private Map E;
   private DVTBuildConsole F;
   private boolean G;
   // $FF: synthetic field
   private static int[] H;

   public AlteraAutoconfigModel(String var1, IProject var2, String var3, String var4, BuildConfigParser.AutoConfigParameters var5, boolean var6) {
      this.p = var2;
      this.q = var3;
      this.r = var1;
      this.s = var4;
      this.t = var5;
      this.G = var6;
      this.F = DVTBuildConsoleRegistry.a(this.b());
   }

   public boolean a() {
      return this.G;
   }

   public IProject b() {
      return this.p;
   }

   public Queue a(String var1) {
      return this.z == null ? null : (Queue)this.z.get(var1);
   }

   public Queue b(String var1) {
      return this.A == null ? null : (Queue)this.A.get(var1);
   }

   public Map c(String var1) {
      return this.B == null ? null : (Map)this.B.get(var1);
   }

   public void a(IPath var1, String var2) {
      if (this.z == null) {
         this.z = new LinkedHashMap();
      }

      Object var3 = (Queue)this.z.get(var2);
      if (var3 == null) {
         var3 = new LinkedList();
      }

      ((Queue)var3).add(var1);
      this.z.put(var2, var3);
   }

   public void b(IPath var1, String var2) {
      if (this.A == null) {
         this.A = new LinkedHashMap();
      }

      Object var3 = (Queue)this.A.get(var2);
      if (var3 == null) {
         var3 = new LinkedList();
      }

      ((Queue)var3).add(var1);
      this.A.put(var2, var3);
   }

   public void a(String var1, String var2) {
      if (this.B == null) {
         this.B = new LinkedHashMap();
      }

      Object var3 = (Map)this.B.get(var2);
      if (var3 == null) {
         var3 = new LinkedHashMap();
      }

      ((Map)var3).put(var1, (Object)null);
      this.B.put(var2, var3);
   }

   public void b(String var1, String var2) {
      if (this.u == null) {
         this.u = new LinkedHashMap();
      }

      Object var3 = (List)this.u.get(var1);
      if (var3 == null) {
         var3 = new ArrayList();
      }

      ((List)var3).add(var2);
      this.u.put(var1, var3);
   }

   public void c(String var1, String var2) {
      if (this.w == null) {
         this.w = new LinkedHashMap();
      }

      Object var3 = (List)this.w.get(var1);
      if (var3 == null) {
         var3 = new ArrayList();
      }

      ((List)var3).add(var2);
      this.w.put(var1, var3);
   }

   public void d(String var1, String var2) {
      if (this.v == null) {
         this.v = new LinkedHashMap();
      }

      Object var3 = (List)this.v.get(var1);
      if (var3 == null) {
         var3 = new ArrayList();
      }

      ((List)var3).add(var2);
      this.v.put(var1, var3);
   }

   public void e(String var1, String var2) {
      if (this.C == null) {
         this.C = new HashMap();
      }

      this.C.put(var1, var2);
   }

   public void f(String var1, String var2) {
      if (this.D == null) {
         this.D = new HashMap();
      }

      this.D.put(var1, var2);
   }

   public void g(String var1, String var2) {
      if (this.E == null) {
         this.E = new HashMap();
      }

      this.E.put(var1, var2);
   }

   public void a(IPath var1) {
      if (this.x == null) {
         this.x = new LinkedList();
      }

      this.x.add(var1);
   }

   public void b(IPath var1) {
      if (this.y == null) {
         this.y = new LinkedList();
      }

      this.y.add(var1);
   }

   public String c() {
      return this.q;
   }

   public String d() {
      return this.s;
   }

   public void e() {
      this.f();
      String var1 = this.C == null ? "" : (String)this.C.get("TOP_LEVEL_ENTITY");
      File var2 = new File(this.s);

      try {
         Throwable var3 = null;
         Object var4 = null;

         try {
            BufferedWriter var5 = new BufferedWriter(new FileWriter(var2, true));

            try {
               StringBuilder var6 = new StringBuilder("Writing build file...");
               DVTTclUtils.a((String)("[Quartus] " + var6), this.b(), 6, DVTDebugKind.QUARTUS_DEBUG_INFO, this.a());
               ((Writer)var5).write("// Generated build file - " + this.r + a);
               if (var1 != null && !var1.isEmpty()) {
                  ((Writer)var5).write(a + "-top " + var1.trim() + a);
               }

               ((Writer)var5).write(a + "+dvt_prepend_init" + a);
               ((Writer)var5).write("<additional_prepend_init_directives>");
               this.a((Writer)var5);
               this.a((Writer)var5, (DVTBuildConsole)this.F);
               this.b((Writer)var5);
               this.a(var5, "verilog", this.u, "-lang vlog", this.F);
               this.a(var5, "vhdl", this.v, "-lang vhdl", this.F);
               this.a(var5, "systemverilog", this.w, "-lang vlog", this.F);
            } finally {
               if (var5 != null) {
                  ((Writer)var5).close();
               }

            }
         } catch (Throwable var14) {
            if (var3 == null) {
               var3 = var14;
            } else if (var3 != var14) {
               var3.addSuppressed(var14);
            }

            throw var3;
         }
      } catch (IOException var15) {
         DVTLogger.INSTANCE.logError((Throwable)var15);
      }

   }

   public void f() {
      if (this.C != null) {
         String var1 = (String)this.C.get("VERILOG_INPUT_VERSION");
         this.m = var1 != null ? var1 : (!this.j() ? "" : this.m);
         String var2 = (String)this.C.get("VHDL_INPUT_VERSION");
         this.n = var2 != null ? var2 : (!this.k() ? "" : this.n);
      }
   }

   private void a(Writer var1) throws IOException {
      StringBuilder var2 = new StringBuilder("Writing syntax mapping to build file...");
      DVTTclUtils.a((String)("[Quartus] " + var2), this.b(), 6, DVTDebugKind.QUARTUS_DEBUG_INFO, this.a());
      if (this.n.equals("VHDL_1987")) {
         var1.write(a + "+dvt_ext_map+VHDL_87+.vhd+.vhdl" + a);
      } else if (this.n.equals("VHDL_1993")) {
         var1.write(a + "+dvt_ext_map+VHDL_93+.vhd+.vhdl" + a);
      } else if (this.n.equals("VHDL_2008")) {
         var1.write(a + "+dvt_ext_map+VHDL_2008+.vhd+.vhdl" + a);
      }

      if (this.m.equals("SYSTEMVERILOG_2005")) {
         var1.write(a + "+dvt_ext_map+SystemVerilog+.v+.vh+.vp+.vs+.vsh+.v95+.v95p+.sv+.svh+.svp+.svi+.sva" + a);
      } else if (this.m.equals("VERILOG_1995")) {
         var1.write(a + "+dvt_ext_map+Verilog_95++.v+.vh+.vp+.vs+.vsh+.v95+.v95p+.sv+.svh+.svp+.svi+.sva" + a);
      } else if (this.m.equals("VERILOG_2001")) {
         var1.write(a + "+dvt_ext_map+Verilog_2001+.v+.vh+.vp+.vs+.vsh+.v95+.v95p+.sv+.svh+.svp+.svi+.sva" + a);
      }

   }

   private void a(Writer var1, DVTBuildConsole var2) throws IOException {
      if (this.j()) {
         StringBuilder var3 = new StringBuilder("Writing global settings to build file...");
         DVTTclUtils.a((String)("[Quartus] " + var3), this.b(), 6, DVTDebugKind.QUARTUS_DEBUG_INFO, this.a());
         var1.write(a + "// -------------------------- Included global library paths -------------------------" + a);
         var1.write("+libext+.v+.sv" + a);
         var1.write("-y $QUARTUS_ROOTDIR/eda/sim_lib" + a);
         File var4 = h != null ? Path.fromOSString(h).append(".altera.quartus").append("quartus2.ini").toFile() : null;
         if (var4 != null && var4.exists()) {
            this.a(var4, var1, var2);
         } else {
            var4 = h != null ? Path.fromOSString(h).append("quartus2.ini").toFile() : null;
            if (var4 != null && var4.exists()) {
               this.a(var4, var1, var2);
            } else {
               var4 = i != null ? Path.fromOSString(i).append("quartus2.ini").toFile() : null;
               if (var4 != null && var4.exists()) {
                  this.a(var4, var1, var2);
               } else {
                  var4 = j != null ? Path.fromOSString(j).append("quartus2.ini").toFile() : null;
                  if (var4 != null && var4.exists()) {
                     this.a(var4, var1, var2);
                  } else {
                     var4 = k != null ? Path.fromOSString(k).append("quartus2.ini").toFile() : null;
                     if (var4 != null && var4.exists()) {
                        this.a(var4, var1, var2);
                     } else {
                        var4 = Path.fromOSString("C:/").append("quartus2.ini").toFile();
                        if (var4.exists()) {
                           this.a(var4, var1, var2);
                        } else {
                           var2.a("No global settings file found!");
                        }
                     }
                  }
               }
            }
         }
      }
   }

   private void a(File var1, Writer var2, DVTBuildConsole var3) {
      try {
         Throwable var4 = null;
         Object var5 = null;

         try {
            BufferedReader var6 = new BufferedReader(new FileReader(var1));

            try {
               if (this.j()) {
                  var3.a("Processing global settings");
                  String var7 = "";

                  while((var7 = var6.readLine()) != null) {
                     if (var7.contains("USER_LIBRARIES")) {
                        String[] var8 = DVTStringUtil.a(l, var7);
                        if (var8 != null && var7.length() >= 3) {
                           String var9 = var8[2];
                           if (var9 != null) {
                              String[] var10 = var9.split(";");
                              if (var10 != null) {
                                 for(String var11 : var10) {
                                    if (!var11.trim().isEmpty()) {
                                       var2.write("-y " + var11 + a);
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }

                  var2.write(a + a);
                  return;
               }
            } finally {
               if (var6 != null) {
                  var6.close();
               }

            }

         } catch (Throwable var22) {
            if (var4 == null) {
               var4 = var22;
            } else if (var4 != var22) {
               var4.addSuppressed(var22);
            }

            throw var4;
         }
      } catch (IOException var23) {
         DVTLogger.INSTANCE.logError((Throwable)var23);
      }
   }

   private void b(Writer var1) throws IOException {
      if (this.j() && this.x != null) {
         StringBuilder var2 = new StringBuilder("Writing search paths to build file...");
         DVTTclUtils.a((String)("[Quartus] " + var2), this.b(), 6, DVTDebugKind.QUARTUS_DEBUG_INFO, this.a());
         var1.write("// -------------------------- Included project library paths -------------------------" + a);

         for(IPath var3 : this.x) {
            this.a(var1, var3, true, false);
         }

         var1.write(a);
      }
   }

   private void a(Writer var1, IPath var2, boolean var3, boolean var4) throws IOException {
      String var5 = var2.isAbsolute() ? var2.toOSString() : Path.fromOSString(this.q).append(var2).toOSString();
      if (var3) {
         this.a(var1, var5);
      }

      if (var4) {
         this.b(var1, var5);
      }

   }

   private void a(Writer var1, String var2) throws IOException {
      if (var2.contains(" ")) {
         var1.write("-y \"" + var2 + "\"" + a);
      } else {
         var1.write("-y " + var2 + a);
      }

   }

   private void a(Writer var1, String var2, Map var3, String var4, DVTBuildConsole var5) throws IOException {
      if (var3 == null) {
         var5.a("No " + var2 + " files found");
      } else if (var3.isEmpty()) {
         var5.a("No " + var2 + " files found");
      } else {
         StringBuilder var6 = (new StringBuilder("Writing ")).append(var2).append(" files to build file...");
         DVTTclUtils.a((String)("[Quartus] " + var6), this.b(), 6, DVTDebugKind.QUARTUS_DEBUG_INFO, this.a());
         this.o = this.o + " " + var4;
         var1.write(a + "// -------------------------- " + var2 + " files --------------------------" + a);
         ArrayList var7 = new ArrayList(var3.keySet());
         Collections.sort(var7);

         for(String var8 : var7) {
            var1.write("+dvt_init -work " + var8 + a);

            for(String var11 : new LinkedHashSet((Collection)var3.get(var8))) {
               IPath var13 = Path.fromOSString(var11);
               this.a(var1, var13, false, true);
            }

            var1.write(a);
         }

         var1.write(a);
      }
   }

   private void b(Writer var1, String var2) throws IOException {
      if (var2.contains(" ")) {
         var1.write("\"" + Paths.get(var2).normalize() + "\"" + a);
      } else {
         var1.write(Paths.get(var2).normalize() + a);
      }

   }

   private boolean j() {
      return this.u != null && !this.u.isEmpty() || this.w != null && !this.w.isEmpty();
   }

   private boolean k() {
      return this.v != null && !this.v.isEmpty();
   }

   public String g() {
      try {
         String var1 = AutoConfig.a(this.t.invocation);
         if (!var1.isEmpty()) {
            BuildConfigParser.AutoConfigParameters var10000 = this.t;
            var10000.reuseText = var10000.reuseText + a + var1 + a;
         }

         String var2 = new String(Files.readAllBytes(Paths.get(this.s)));
         var2 = var2.replace("<additional_prepend_init_directives>", this.t.reuseText);
         return var2;
      } catch (IOException var3) {
         return "";
      }
   }

   public Queue a(String var1, AlteraFileType var2) {
      switch (i()[var2.ordinal()]) {
         case 1:
            return this.a(var1);
         case 2:
            return this.b(var1);
         default:
            return null;
      }
   }

   public boolean h() {
      return this.G;
   }

   // $FF: synthetic method
   static int[] i() {
      int[] var10000 = H;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[AlteraFileType.values().length];

         try {
            var0[AlteraFileType.QIP_FILE.ordinal()] = 1;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[AlteraFileType.QSF_FILE.ordinal()] = 2;
         } catch (NoSuchFieldError var1) {
         }

         H = var0;
         return var0;
      }
   }
}
