package ro.amiq.dvt.altera.autoconfig;

import ro.amiq.dvt.tcl.model.DVTTclCommand;
import ro.amiq.dvt.tcl.model.DVTTclInterp;
import ro.amiq.dvt.tcl.model.IDVTCommandType;
import tcl.lang.TclException;
import tcl.lang.TclObject;

public abstract class AlteraAutoconfigCommand extends DVTTclCommand {
   private AlteraAutoconfigModel c;

   protected AlteraAutoconfigCommand(IDVTCommandType var1) {
      super(var1);
   }

   public void a(DVTTclInterp var1, TclObject[] var2) throws TclException {
   }

   void a(AlteraAutoconfigModel var1) {
      this.c = var1;
   }

   public AlteraAutoconfigModel a() {
      return this.c;
   }
}
