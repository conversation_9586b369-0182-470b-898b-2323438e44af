package ro.amiq.dvt.altera.autoconfig;

import java.io.File;
import java.io.IOException;
import java.nio.file.FileVisitOption;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.EnumSet;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Queue;
import java.util.Set;
import java.util.stream.Collectors;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.buildconfig.BuildConfigManager;
import ro.amiq.dvt.buildconfig.BuildConfigParser;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.builders.DVTBuildConsoleRegistry;
import ro.amiq.dvt.model.problems.DVTProblem;
import ro.amiq.dvt.model.problems.DVTProblemManager;
import ro.amiq.dvt.model.problems.Severity;
import ro.amiq.dvt.model.problems.TclProblemKinds;
import ro.amiq.dvt.startup.core.DVTLogger;
import ro.amiq.dvt.tcl.model.DVTDebugKind;
import ro.amiq.dvt.tcl.model.DVTTclCommand;
import ro.amiq.dvt.tcl.model.DVTTclInterp;
import ro.amiq.dvt.tcl.model.DVTTclUtils;
import tcl.lang.TclException;

public class AlteraAutoconfigTCLInterp extends DVTTclInterp {
   private static final String e = "::quartus(qsf_path)";
   private static final String f = "::quartus(qip_path)";
   private IProject g;
   private AlteraAutoconfigModel h;
   private String i = "";
   // $FF: synthetic field
   private static int[] j;

   public AlteraAutoconfigTCLInterp(IProgressMonitor var1, IProject var2, AlteraAutoconfigModel var3) {
      super(var1);
      this.g = var2;
      this.h = var3;
   }

   public void a(String var1, String var2, BuildConfigParser.AutoConfigParameters var3) {
      try {
         DVTBuildConsole var4 = DVTBuildConsoleRegistry.a(this.d());
         var4.a("Processing file " + ((new File(var1)).isAbsolute() ? var1 : Path.fromOSString(var2).append(var1)));
         this.a(var1, var2, this.g);
         Map var12 = this.h.c(var1);
         String var6 = BuildConfigManager.g(this.g, var3.invocation);
         List var7 = BuildConfigManager.r(this.g, var3.invocation);
         if (var7 != null && !var7.isEmpty()) {
            if (var12 != null && !var12.isEmpty()) {
               this.a(var1, var12, var7, var6);
            }

            if (var12 != null) {
               for(String var8 : var12.keySet()) {
                  if (this.c.isCanceled()) {
                     return;
                  }

                  String var10 = BuildConfigManager.l(this.g);
                  var4.a("*** Warning: The following .qip file couldn't be found:\n\t" + var8 + ".qip" + System.lineSeparator() + "Either manually specify it " + var10 + " using:\n\t" + "+dvt_autoconfig_quartus_qip+/path/to/qip_dir/" + var1 + ".qip\n" + "or add the appropriate search directory in " + var10 + " using:\n\t" + "+dvt_autoconfig_quartus_qip_search_path+/path/to/qip_dir \n");
               }
            }
         }

         this.a(var1, var2, var3, AlteraFileType.QSF_FILE);
         this.a(var1, var2, var3, AlteraFileType.QIP_FILE);
      } catch (IOException var11) {
         String var5 = "DVTTclInterp: " + var11.getMessage();
         DVTTclUtils.a(var5, this.d(), this.e(), DVTDebugKind.TCL_ERROR, false);
      }

   }

   public void a(DVTBuildConsole var1, String var2) {
      var1.a(var2 + "\n");
   }

   public void a(String var1, String var2, BuildConfigParser.AutoConfigParameters var3, AlteraFileType var4) {
      try {
         Queue var5 = this.h.a(var1, var4);
         if (var5 == null) {
            return;
         }

         while(!var5.isEmpty()) {
            IPath var11 = (IPath)var5.poll();
            IPath var7 = var11.toFile().isAbsolute() ? var11 : Path.fromOSString(var2).append(var11);
            IPath var8 = var7.removeLastSegments(1);
            String var9 = var11.lastSegment();
            this.setVar(this.a(var4), var8.toOSString(), 131072);
            this.a(var9);
            this.a(var11.toOSString(), var2, var3);
         }
      } catch (TclException var10) {
         String var6 = "DVTTclInterp: " + var10.getMessage();
         DVTTclUtils.a(var6, this.d(), this.e(), DVTDebugKind.TCL_ERROR, false);
      }

   }

   private void a(String var1, Map var2, List var3, String var4) throws IOException {
      Set var5 = var2.keySet();
      if (var5 != null && !var5.isEmpty()) {
         DVTQuartusMonitorFileVisitor var6 = new DVTQuartusMonitorFileVisitor(var1, Path.fromOSString(var4), var2, this.h, this.c);
         DVTBuildConsole var7 = DVTBuildConsoleRegistry.a(this.d());
         String var8 = "Searching for the following qip files: ";
         var7.a(var8 + (String)var5.stream().limit(5L).collect(Collectors.joining(", ")) + (var2.size() > 5 ? ", ..." : ""));
         DVTLogger.INSTANCE.logInfo(var8 + (String)var5.stream().collect(Collectors.joining(", ")));

         for(String var9 : var3) {
            if (this.c.isCanceled()) {
               return;
            }

            if ("COMPILATION_ROOT".equals(var9)) {
               var9 = var4;
            }

            if (var2.isEmpty()) {
               return;
            }

            Files.walkFileTree(Paths.get(var9), EnumSet.noneOf(FileVisitOption.class), Integer.MAX_VALUE, var6);
            Iterator var11 = var2.entrySet().iterator();

            while(var11.hasNext()) {
               Map.Entry var12 = (Map.Entry)var11.next();
               IPath var13 = (IPath)var12.getValue();
               if (var13 != null) {
                  this.h.a(var13, var1);
                  var11.remove();
               }
            }
         }

      }
   }

   protected boolean a() {
      return this.h != null;
   }

   protected void b() {
      DVTQuartusCommandType[] var4;
      for(DVTQuartusCommandType var1 : var4 = DVTQuartusCommandType.values()) {
         this.a((DVTTclCommand)var1.a(this.h));
      }

   }

   protected void c() {
   }

   protected void a(String var1, int var2, String var3) {
      if (this.h != null) {
         DVTProblem var4 = new DVTProblem(this.h.b(), TclProblemKinds.a, var3, false, var2 + 1, new String[]{var1});
         var4.setSeverity(Severity.ERROR);
         var4.setToConsole(true);
         DVTProblemManager.a().a(var4);
      }
   }

   public IProject d() {
      return this.g;
   }

   public int e() {
      return 6;
   }

   public void a(Exception var1, String var2, int var3, String var4) {
   }

   public String a(AlteraFileType var1) {
      switch (h()[var1.ordinal()]) {
         case 1:
            return "::quartus(qip_path)";
         case 2:
            return "::quartus(qsf_path)";
         default:
            return "";
      }
   }

   public String f() {
      return this.i;
   }

   public void a(String var1) {
      this.i = var1;
   }

   public boolean g() {
      return this.h == null ? false : this.h.a();
   }

   // $FF: synthetic method
   static int[] h() {
      int[] var10000 = j;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[AlteraFileType.values().length];

         try {
            var0[AlteraFileType.QIP_FILE.ordinal()] = 1;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[AlteraFileType.QSF_FILE.ordinal()] = 2;
         } catch (NoSuchFieldError var1) {
         }

         j = var0;
         return var0;
      }
   }
}
