package ro.amiq.dvt.altera.autoconfig;

import java.io.IOException;
import java.nio.file.FileVisitResult;
import java.nio.file.FileVisitor;
import java.nio.file.Path;
import java.nio.file.attribute.BasicFileAttributes;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.builders.DVTBuildConsoleRegistry;

public class DVTQuartusMonitorFileVisitor implements FileVisitor {
   static final String a = ".qip";
   private final IProgressMonitor b;
   private IPath c;
   private Map d;
   private AlteraAutoconfigModel e;
   private String f;
   private Set g;

   DVTQuartusMonitorFileVisitor(String var1, IPath var2, Map var3, AlteraAutoconfigModel var4, IProgressMonitor var5) {
      this.c = var2;
      this.d = var3;
      this.e = var4;
      this.f = var1;
      this.b = var5;
      this.g = new HashSet();
   }

   public FileVisitResult a(Path var1, BasicFileAttributes var2) throws IOException {
      if (this.g.contains(var1)) {
         return FileVisitResult.SKIP_SUBTREE;
      } else {
         this.g.add(var1);
         return FileVisitResult.CONTINUE;
      }
   }

   public FileVisitResult b(Path var1, BasicFileAttributes var2) throws IOException {
      if (this.b.isCanceled()) {
         return FileVisitResult.TERMINATE;
      } else if (var1 == null) {
         return FileVisitResult.CONTINUE;
      } else {
         String var3 = var1.toString();
         if (!var3.endsWith(".qip")) {
            return FileVisitResult.CONTINUE;
         } else {
            IPath var4 = org.eclipse.core.runtime.Path.fromOSString(var3);
            String var5 = var4.removeFileExtension().lastSegment();
            if (this.d.containsKey(var5)) {
               IPath var6 = var4.makeRelativeTo(this.c);
               this.d.put(var5, var6);
               DVTBuildConsole var7 = DVTBuildConsoleRegistry.a(this.e.b());
               var7.a("Found qip file: " + var4 + " included by " + this.f);
            }

            return FileVisitResult.CONTINUE;
         }
      }
   }

   public FileVisitResult a(Path var1, IOException var2) throws IOException {
      return FileVisitResult.CONTINUE;
   }

   public FileVisitResult b(Path var1, IOException var2) throws IOException {
      return FileVisitResult.CONTINUE;
   }

   // $FF: synthetic method
   public FileVisitResult visitFileFailed(Object var1, IOException var2) throws IOException {
      return this.a((Path)var1, var2);
   }

   // $FF: synthetic method
   public FileVisitResult postVisitDirectory(Object var1, IOException var2) throws IOException {
      return this.b((Path)var1, var2);
   }

   // $FF: synthetic method
   public FileVisitResult visitFile(Object var1, BasicFileAttributes var2) throws IOException {
      return this.b((Path)var1, var2);
   }

   // $FF: synthetic method
   public FileVisitResult preVisitDirectory(Object var1, BasicFileAttributes var2) throws IOException {
      return this.a((Path)var1, var2);
   }
}
