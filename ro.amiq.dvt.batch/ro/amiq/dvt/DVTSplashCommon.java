package ro.amiq.dvt;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import org.eclipse.core.resources.IProject;
import ro.amiq.dvt.flclient.DFLI;
import ro.amiq.dvt.utils.DVTUtilsCommon;
import ro.amiq.dvt.utils.Utils;

abstract class DVTSplashCommon {
   protected static final boolean a = System.getenv("DVT_LICENSE_CORE_CHECKOUT_FORCE") != null;
   protected static final Map b = new HashMap();

   static {
      b.put("F65", "ro.amiq.edt.enature");
      b.put("F736C6E", "ro.amiq.slndt.slnnature");
      b.put("F73646C", "ro.amiq.msdldt.msdlnature");
      b.put("F707373", "ro.amiq.pssdt.pssnature");
      b.put("F7376", "ro.amiq.vlogdt.VlogNature");
      b.put("*********", "ro.amiq.vhdldt.VhdlNature");
      b.put("F6370705F657874", "ro.amiq.dvt.cdt.cppextnature");
   }

   public boolean b() {
      return !a && this.e().isEmpty() ? false : this.b("F636F7265");
   }

   public boolean c() {
      return this.b("F636F7265");
   }

   public boolean a(IProject var1, String var2) {
      try {
         if (var1 == null || !var1.isAccessible()) {
            return false;
         }

         if (var2.equals("F636F7265")) {
            if (!Utils.getArgumentValue(var1)) {
               return false;
            }
         } else {
            if (!b.containsKey(var2)) {
               return false;
            }

            if (!var1.hasNature((String)b.get(var2))) {
               return false;
            }
         }
      } catch (Exception var3) {
         return false;
      }

      return this.b("F636F7265") && this.b(var2);
   }

   public abstract boolean a(String... var1);

   public void a(String var1) {
      this.c(var1);
   }

   public void f() {
   }

   public void d() {
      Set var1 = this.e();

      for(Map.Entry var2 : b.entrySet()) {
         if (var1.contains(var2.getValue())) {
            this.b((String)var2.getKey());
         } else {
            this.c((String)var2.getKey());
         }
      }

      if (!a && var1.isEmpty()) {
         this.c("F636F7265");
      } else {
         this.b("F636F7265");
      }

   }

   protected abstract Set e();

   private boolean b(String var1) {
      return DFLI.I.logErrorAndExit(DVTUtilsCommon.INSTANCE.dec(var1));
   }

   private void c(String var1) {
      DFLI.I.getArgumentValue(DVTUtilsCommon.INSTANCE.dec(var1));
   }
}
