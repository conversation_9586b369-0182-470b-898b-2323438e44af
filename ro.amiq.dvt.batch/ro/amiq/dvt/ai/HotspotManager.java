package ro.amiq.dvt.ai;

import java.util.Set;
import java.util.function.BooleanSupplier;
import org.eclipse.lsp4j.TextDocumentPositionParams;
import ro.amiq.dvt.ai.model.EditorPosition;
import ro.amiq.dvt.ls.LSEditorsManager;

public class HotspotManager extends HotspotManagerCommon {
   private static final Object a = new Object();
   private static HotspotManager b;

   public static HotspotManager a() {
      if (b == null) {
         synchronized(a) {
            b = new HotspotManager();
         }
      }

      return b;
   }

   protected Set b() {
      return LSEditorsManager.INSTANCE.getOpenEditorsFullPaths();
   }

   public void a(BooleanSupplier var1, TextDocumentPositionParams var2) {
      if (!var1.getAsBoolean()) {
         EditorPosition var3 = AIUtils.a().a(var2);
         if (var3 != null) {
            this.a(var3);
         }
      }
   }
}
