package ro.amiq.dvt.ai.contributor;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.function.BooleanSupplier;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.NullProgressMonitor;
import org.eclipse.jface.viewers.ILabelProvider;
import org.eclipse.jface.viewers.LabelProvider;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.AIConstants;
import ro.amiq.dvt.ai.AIExpansionUtils;
import ro.amiq.dvt.ai.model.EditorPosition;
import ro.amiq.dvt.ai.model.EditorRange;
import ro.amiq.dvt.ai.model.SnippetSelectionType;
import ro.amiq.dvt.model.reflection.IReportHitsListener;
import ro.amiq.dvt.model.reflection.IRfActionBlockElement;
import ro.amiq.dvt.model.reflection.IRfAssociatedTypeElement;
import ro.amiq.dvt.model.reflection.IRfClassElement;
import ro.amiq.dvt.model.reflection.IRfDefContainer;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfDesignElement;
import ro.amiq.dvt.model.reflection.IRfEntityComplement;
import ro.amiq.dvt.model.reflection.IRfInterface;
import ro.amiq.dvt.model.reflection.IRfListType;
import ro.amiq.dvt.model.reflection.IRfMethodElement;
import ro.amiq.dvt.model.reflection.IRfNamedElement;
import ro.amiq.dvt.model.reflection.IRfPackageElement;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.model.reflection.util.RfReferencesUtils;
import ro.amiq.dvt.model.reflection.util.RfSearchUtils;
import ro.amiq.dvt.ui.editor.DVTEditor;
import ro.amiq.dvt.ui.guifilters.DVTGUIFilterMatcher;
import ro.amiq.dvt.ui.guifilters.DVTSearchGUIFilterMatcher;
import ro.amiq.dvt.ui.guifilters.ViewSet;
import ro.amiq.dvt.ui.search.FileSynchedAndAvailableManager;
import ro.amiq.dvt.ui.search.SearchHit;
import ro.amiq.dvt.ui.views.IDVTElementWrapper;
import ro.amiq.dvt.utils.DVTFileUtils;
import ro.amiq.dvt.utils.parser.IDVTFileInstance;

public interface IAIContributor {
   // $FF: synthetic field
   int[] a;

   IDVTElementWrapper a(IRfDefContainer var1);

   LanguageKind a();

   default boolean a(IDVTElementWrapper var1) {
      return false;
   }

   default String a(IRfNamedElement var1) {
      if (var1 instanceof IRfAssociatedTypeElement && !(var1 instanceof IRfEntityComplement)) {
         IRfNamedElement var2 = ((IRfAssociatedTypeElement)var1).getAssociatedType();
         if (var2 instanceof IRfListType) {
            return AIExpansionUtils.INSTANCE.expandArrayTypedef((IRfAssociatedTypeElement)var1);
         }

         if (var2 != null) {
            return AIExpansionUtils.INSTANCE.expandNamedElementOrMacro(var2);
         }
      }

      return AIExpansionUtils.INSTANCE.expandDefElement(var1.getDeclaration(), (DVTEditor)null, true);
   }

   default Set a(SnippetSelectionType var1) {
      switch (c()[var1.ordinal()]) {
         case 3:
            return AIConstants.CONTAINER_TYPES;
         case 4:
            return new HashSet(Arrays.asList(IRfNamedElement.class));
         case 5:
            return new HashSet(Arrays.asList(IRfActionBlockElement.class));
         case 6:
            return new HashSet(Arrays.asList(IRfMethodElement.class));
         case 7:
         default:
            return null;
         case 8:
            return new HashSet(Arrays.asList(IRfClassElement.class));
         case 9:
         case 12:
            return new HashSet(Arrays.asList(IRfDesignElement.class));
         case 10:
            return new HashSet(Arrays.asList(IRfInterface.class));
         case 11:
            return new HashSet(Arrays.asList(IRfPackageElement.class));
         case 13:
         case 14:
            return new HashSet(Arrays.asList(IRfEntityComplement.class));
      }
   }

   default Set b() {
      return AIConstants.CONTAINER_TYPES_WITHOUT_METHODS;
   }

   default void a(final Set var1, IRfNamedElement var2, IProject var3) {
      Object var4 = null;
      String var10 = var2.getName();
      NullProgressMonitor var5 = new NullProgressMonitor();
      final HashSet var6 = new HashSet();
      HashMap var7 = new HashMap();
      FileSynchedAndAvailableManager var8 = new FileSynchedAndAvailableManager(RfReferencesUtils.getSearchedFiles(var7), var5);
      IReportHitsListener var9 = new IReportHitsListener() {
         public void a(SearchHit var1x) {
            if (!var6.contains(var1x)) {
               IProject var2 = var1x.t();
               if (var2 != null) {
                  ParserPath var3 = var1x.f();
                  if (var3 != null) {
                     DVTGUIFilterMatcher.GUIFilterResult var4 = DVTSearchGUIFilterMatcher.a(ViewSet.ViewName.SEARCH, var3.path, var1x.k(), var2);
                     if (var4 == DVTGUIFilterMatcher.GUIFilterResult.NOT_FILTERED) {
                        IFile var5 = DVTFileUtils.a().b(var2, var3.path);
                        if (var5 != null) {
                           var6.add(var1x);
                           var1.add(new EditorRange(var5, var1x.e(), var1x.e() + var1x.l()));
                        }
                     }
                  }
               }
            }
         }

         public Set getMatches() {
            return null;
         }

         // $FF: synthetic method
         public void addMatch(Object var1x) {
            this.a((SearchHit)var1x);
         }
      };
      RfSearchUtils.computeReferences(var9, var3, var2, var10, var5, var7, var8, false, false, false, false);
   }

   default void a(List var1) {
      var1.sort((var0, var1x) -> {
         int var2 = var0.f();
         int var3 = var1x.f();
         if (var2 != var3) {
            return Integer.compare(var2, var3);
         } else {
            boolean var4 = var0.a();
            boolean var5 = var1x.a();
            if (var4 && var5) {
               return var0.d().compareTo(var1x.d());
            } else if (var4) {
               return 1;
            } else if (var5) {
               return -1;
            } else {
               IRfDefElement var6 = var0.e();
               IRfDefElement var7 = var1x.e();
               if (var6 != null && var7 != null) {
                  String var8 = var6.getName();
                  String var9 = var7.getName();
                  return var8 != null && var9 != null ? var8.compareTo(var9) : 0;
               } else {
                  return 0;
               }
            }
         }
      });
   }

   default ILabelProvider a(DVTEditor var1) {
      return new LabelProvider() {
         public String getText(Object var1) {
            return var1 == null ? "" : var1.toString();
         }
      };
   }

   default String a(IRfDefElement var1, DVTEditor var2, boolean var3) {
      return "";
   }

   default List a(IRfDefElement var1, EditorPosition var2, BooleanSupplier var3) {
      return null;
   }

   default boolean b(IDVTElementWrapper var1) {
      return var1 == null ? false : var1.getRfElement() instanceof IDVTFileInstance;
   }

   default ParserPath c(IDVTElementWrapper var1) {
      if (var1 == null) {
         return null;
      } else {
         IDVTFileInstance var2 = (IDVTFileInstance)var1.getRfElement(IDVTFileInstance.class);
         return var2 == null ? null : var2.getParserPath();
      }
   }

   // $FF: synthetic method
   static int[] c() {
      int[] var10000 = a;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[SnippetSelectionType.values().length];

         try {
            var0[SnippetSelectionType.ACTION_BLOCK.ordinal()] = 5;
         } catch (NoSuchFieldError var14) {
         }

         try {
            var0[SnippetSelectionType.ARCHITECTURE.ordinal()] = 13;
         } catch (NoSuchFieldError var13) {
         }

         try {
            var0[SnippetSelectionType.CLASS.ordinal()] = 8;
         } catch (NoSuchFieldError var12) {
         }

         try {
            var0[SnippetSelectionType.CODE.ordinal()] = 1;
         } catch (NoSuchFieldError var11) {
         }

         try {
            var0[SnippetSelectionType.CONFIGURATION.ordinal()] = 14;
         } catch (NoSuchFieldError var10) {
         }

         try {
            var0[SnippetSelectionType.CONTAINER.ordinal()] = 3;
         } catch (NoSuchFieldError var9) {
         }

         try {
            var0[SnippetSelectionType.ELEMENT.ordinal()] = 4;
         } catch (NoSuchFieldError var8) {
         }

         try {
            var0[SnippetSelectionType.ENTITY.ordinal()] = 12;
         } catch (NoSuchFieldError var7) {
         }

         try {
            var0[SnippetSelectionType.FILE.ordinal()] = 2;
         } catch (NoSuchFieldError var6) {
         }

         try {
            var0[SnippetSelectionType.INTERFACE.ordinal()] = 10;
         } catch (NoSuchFieldError var5) {
         }

         try {
            var0[SnippetSelectionType.METHOD.ordinal()] = 6;
         } catch (NoSuchFieldError var4) {
         }

         try {
            var0[SnippetSelectionType.MODULE.ordinal()] = 9;
         } catch (NoSuchFieldError var3) {
         }

         try {
            var0[SnippetSelectionType.PACKAGE.ordinal()] = 11;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[SnippetSelectionType.PORT_LIST.ordinal()] = 7;
         } catch (NoSuchFieldError var1) {
         }

         a = var0;
         return var0;
      }
   }
}
