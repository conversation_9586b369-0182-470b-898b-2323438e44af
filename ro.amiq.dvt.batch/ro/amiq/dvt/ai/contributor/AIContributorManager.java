package ro.amiq.dvt.ai.contributor;

import java.util.Arrays;
import java.util.List;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.vhdldt.ai.VhdlAIContributor;
import ro.amiq.vlogdt.ai.VlogAIContributor;

public enum AIContributorManager {
   INSTANCE;

   private final VlogAIContributor vlogAIContributor = new VlogAIContributor();
   private final VhdlAIContributor vhdlAIContributor = new VhdlAIContributor();
   // $FF: synthetic field
   private static int[] a;

   public List getContributors() {
      return Arrays.asList(this.vlogAIContributor, this.vhdlAIContributor);
   }

   public IAIContributor getContributor(LanguageKind var1) {
      if (var1 == null) {
         return null;
      } else {
         switch (var1) {
            case VLOG:
               return this.vlogAIContributor;
            case VHDL:
               return this.vhdlAIContributor;
            default:
               return null;
         }
      }
   }

   // $FF: synthetic method
   static int[] $SWITCH_TABLE$ro$amiq$dvt$LanguageKind() {
      int[] var10000 = a;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[LanguageKind.values().length];

         try {
            var0[LanguageKind.BC.ordinal()] = 13;
         } catch (NoSuchFieldError var13) {
         }

         try {
            var0[LanguageKind.CPP.ordinal()] = 8;
         } catch (NoSuchFieldError var12) {
         }

         try {
            var0[LanguageKind.CPP_EXT.ordinal()] = 9;
         } catch (NoSuchFieldError var11) {
         }

         try {
            var0[LanguageKind.E.ordinal()] = 2;
         } catch (NoSuchFieldError var10) {
         }

         try {
            var0[LanguageKind.MSDL.ordinal()] = 4;
         } catch (NoSuchFieldError var9) {
         }

         try {
            var0[LanguageKind.PF.ordinal()] = 11;
         } catch (NoSuchFieldError var8) {
         }

         try {
            var0[LanguageKind.PSS.ordinal()] = 5;
         } catch (NoSuchFieldError var7) {
         }

         try {
            var0[LanguageKind.SLN.ordinal()] = 3;
         } catch (NoSuchFieldError var6) {
         }

         try {
            var0[LanguageKind.SO.ordinal()] = 12;
         } catch (NoSuchFieldError var5) {
         }

         try {
            var0[LanguageKind.TCL.ordinal()] = 10;
         } catch (NoSuchFieldError var4) {
         }

         try {
            var0[LanguageKind.UNKNOWN.ordinal()] = 1;
         } catch (NoSuchFieldError var3) {
         }

         try {
            var0[LanguageKind.VHDL.ordinal()] = 7;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[LanguageKind.VLOG.ordinal()] = 6;
         } catch (NoSuchFieldError var1) {
         }

         a = var0;
         return var0;
      }
   }
}
