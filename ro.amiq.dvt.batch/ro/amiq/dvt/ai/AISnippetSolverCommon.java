package ro.amiq.dvt.ai;

import com.google.common.collect.ImmutableMap;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;
import org.eclipse.core.resources.IProject;
import org.eclipse.jface.viewers.ILabelProvider;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.contributor.AIContributorManager;
import ro.amiq.dvt.ai.contributor.IAIContributor;
import ro.amiq.dvt.ai.model.CodeAndLineRange;
import ro.amiq.dvt.ai.model.CodeMatchPriority;
import ro.amiq.dvt.ai.model.DependenciesExpansionStrategy;
import ro.amiq.dvt.ai.model.Dependency;
import ro.amiq.dvt.ai.model.DependencySource;
import ro.amiq.dvt.ai.model.EditorPosition;
import ro.amiq.dvt.ai.model.EditorRange;
import ro.amiq.dvt.ai.model.LogSeverity;
import ro.amiq.dvt.ai.model.LogTarget;
import ro.amiq.dvt.ai.model.MultiRootTree;
import ro.amiq.dvt.ai.model.SnippetExamplesOfType;
import ro.amiq.dvt.ai.model.SnippetOutlineOfSelectedType;
import ro.amiq.dvt.ai.model.SnippetSelectionType;
import ro.amiq.dvt.ai.model.TreeElement;
import ro.amiq.dvt.ai.model.exceptions.AIExceptionData;
import ro.amiq.dvt.ai.model.exceptions.AIExceptionKind;
import ro.amiq.dvt.ai.model.exceptions.AINoDHAvailableException;
import ro.amiq.dvt.ai.model.exceptions.AINoVHAvailableException;
import ro.amiq.dvt.core.DVTNature;
import ro.amiq.dvt.model.reflection.ElementPath;
import ro.amiq.dvt.model.reflection.IMacroInfo;
import ro.amiq.dvt.model.reflection.IRfBreadcrumbElement;
import ro.amiq.dvt.model.reflection.IRfCompositeType;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfEnumElement;
import ro.amiq.dvt.model.reflection.IRfInstanceElement;
import ro.amiq.dvt.model.reflection.IRfNamedElement;
import ro.amiq.dvt.model.reflection.IRfNamedElementAndScope;
import ro.amiq.dvt.model.reflection.IRfSingleLangProject;
import ro.amiq.dvt.model.reflection.IRfTypeAliasElement;
import ro.amiq.dvt.model.reflection.IRfVHBreadcrumbElement;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.model.reflection.RfMixedLangManager;
import ro.amiq.dvt.ui.editor.DVTEditor;
import ro.amiq.dvt.ui.editor.breadcrumb.AbstractBreadcrumbUtilsCommon;
import ro.amiq.dvt.ui.editor.breadcrumb.BreadcrumbInput;
import ro.amiq.dvt.ui.editor.breadcrumb.BreadcrumbSegment;
import ro.amiq.dvt.ui.editor.dhbreadcrumb.DHBreadcrumbUtils;
import ro.amiq.dvt.ui.editor.dhbreadcrumb.DHBreadcrumbViewer;
import ro.amiq.dvt.ui.editor.vhbreadcrumb.VHBreadcrumbUtils;
import ro.amiq.dvt.ui.editor.vhbreadcrumb.VHBreadcrumbViewer;
import ro.amiq.dvt.ui.views.IDVTElementWrapper;
import ro.amiq.dvt.ui.views.lazy.tree.TreePath;
import ro.amiq.dvt.ui.views.lazy.views.designhierarchy.LazyElDesignHierarchyModel;

public abstract class AISnippetSolverCommon {
   protected static final Map a = (new ImmutableMap.Builder()).put("Verilog/SystemVerilog Language", "SystemVerilog").put("VHDL Language", "VHDL").put("e Language", "e Language").put("PSS Language", "PSS").put("Build Config", "DVT Build Configuration File").build();
   // $FF: synthetic field
   private static int[] b;

   public List a(BooleanSupplier var1) {
      IProject var2 = AIUtils.a().d();
      if (var2 == null) {
         return null;
      } else {
         List var3 = DVTNature.b(var2);
         ArrayList var4 = new ArrayList();

         for(String var5 : var3) {
            AIUtils.a().a(var1, "language");
            String var7 = (String)a.get(var5);
            if (var7 != null) {
               var4.add(var7);
            }
         }

         return var4;
      }
   }

   public String a(EditorPosition var1, SnippetSelectionType var2, DVTEditor var3, BooleanSupplier var4) {
      IRfNamedElementAndScope var5 = AIUtils.a().a(var1);
      if (var5 == null) {
         return null;
      } else {
         AIUtils.a().a(var4, "selected");
         return AIExpansionUtils.INSTANCE.getNamedElementExpandedForSelectionType(var5, var2, var3);
      }
   }

   public List a(SnippetOutlineOfSelectedType var1, EditorPosition var2, BooleanSupplier var3) {
      if (var2 == null) {
         return null;
      } else {
         int var4 = AIProtectManager.INSTANCE.getProtectLineNumber(var2.c());
         if (var4 > 0) {
            AIUtils.a().b(var2.c(), var4);
         }

         if (SnippetOutlineOfSelectedType.FILE.equals(var1)) {
            return this.b(var2.c());
         } else {
            IRfNamedElementAndScope var5 = AIUtils.a().a(var2);
            if (var5 == null) {
               return null;
            } else {
               LanguageKind var6 = var5.getLanguageKind();
               IAIContributor var7 = AIContributorManager.INSTANCE.getContributor(var6);
               if (var7 == null) {
                  return null;
               } else {
                  AIUtils.a().a(var3, "outline of");
                  IRfDefElement var8;
                  switch (d()[var1.ordinal()]) {
                     case 2:
                        var8 = AIUtils.a().a(var5, var7.a(SnippetSelectionType.CONTAINER), true);
                        break;
                     case 3:
                        var8 = AIUtils.a().a(var5, var7.a(SnippetSelectionType.CONTAINER), false);
                        break;
                     default:
                        return null;
                  }

                  if (var8 == null) {
                     return null;
                  } else {
                     IRfNamedElement var9 = var8.getNamedElement();
                     if (var9 instanceof IRfTypeAliasElement) {
                        IRfNamedElement var10 = ((IRfTypeAliasElement)var9).getTranslatedType();
                        if (var10 != null) {
                           var8 = var10.getDeclaration();
                        }
                     }

                     AIUtils.a().a(var3, "outline of");
                     IDVTElementWrapper var12 = this.a(var8);
                     if (var12 == null) {
                        return null;
                     } else {
                        AIUtils.a().a(var3, "outline of");
                        ArrayList var11 = new ArrayList();
                        var11.add(this.a(var12));
                        return var11;
                     }
                  }
               }
            }
         }
      }
   }

   public List a(String var1, BooleanSupplier var2) {
      boolean var3 = !var1.contains("*");
      ArrayList var4 = new ArrayList();
      if (var1.startsWith("file:")) {
         for(String var12 : AISymbolSolver.INSTANCE.getAbsolutePathsFromFileQuery(var1)) {
            AIUtils.a().a(var2, "outline of");
            int var14 = AIProtectManager.INSTANCE.getProtectLineNumber(var12);
            if (var14 > 0) {
               if (var3) {
                  AIUtils.a().b(var12, var14);
               }
            } else {
               var4.add(new MultiRootTree(this.b(var12)));
            }
         }

         return var4;
      } else {
         for(IRfNamedElement var6 : AISymbolSolver.INSTANCE.getNamedElementsByName(var1)) {
            AIUtils.a().a(var2, "outline of");
            if (!(var6 instanceof IMacroInfo)) {
               if (var6 instanceof IRfTypeAliasElement) {
                  var6 = ((IRfTypeAliasElement)var6).getTranslatedType();
               }

               String var8 = AIUtils.a().b((IRfNamedElement)var6);
               if (var8 != null) {
                  int var9 = AIProtectManager.INSTANCE.getProtectLineNumber(var8);
                  if (var9 > 0) {
                     if (var3) {
                        AIUtils.a().b(var8, var9);
                     }
                  } else {
                     IDVTElementWrapper var10 = this.a(var6.getDeclaration());
                     if (var10 != null) {
                        var4.add(new MultiRootTree(new ArrayList(Arrays.asList(this.a(var10)))));
                     }
                  }
               }
            }
         }

         return var4;
      }
   }

   public List b(String var1, BooleanSupplier var2) {
      if (var1.startsWith("file:")) {
         boolean var8 = AIUtils.a().f(var1);
         int var9 = var1.lastIndexOf(58);
         List var10 = AISymbolSolver.INSTANCE.getAbsolutePathsFromFileQuery(var8 ? var1.substring(0, var9) : var1);
         AIUtils.a().a(var2, "filename of");
         return (List)var10.stream().map((var0) -> Paths.get(var0).getFileName().toString()).collect(Collectors.toList());
      } else {
         List var3 = AISymbolSolver.INSTANCE.getNamedElementsByName(var1);
         HashSet var4 = new HashSet();

         for(IRfNamedElement var5 : var3) {
            AIUtils.a().a(var2, "filename of");
            if (var5 instanceof IMacroInfo) {
               ParserPath var7 = var5.getMacroParserPath();
               if (var7 != null && var7.path != null) {
                  var4.add(Paths.get(var7.path).getFileName().toString());
               }
            } else {
               String var11 = AIUtils.a().b((IRfNamedElement)var5);
               if (var11 != null) {
                  var4.add(Paths.get(var11).getFileName().toString());
               }
            }
         }

         return new ArrayList(var4);
      }
   }

   public List a(SnippetExamplesOfType var1, int var2, BooleanSupplier var3) {
      ArrayList var4 = new ArrayList();
      Map var5 = AISymbolSolver.INSTANCE.getNamedElementsMatchingType(var1);
      if (var5 == null) {
         return var4;
      } else {
         AIUtils.a().a(var3, "examples of");
         ArrayList var6 = new ArrayList();

         for(Map.Entry var7 : var5.entrySet()) {
            if (!AIProtectManager.INSTANCE.isFileProtected(((EditorRange)var7.getValue()).b())) {
               var6.add(new MatchAndPriority(var7.getKey(), this.a((EditorRange)var7.getValue())));
            }
         }

         var6.sort((var0, var1x) -> Integer.compare(var0.b.getPriority(), var1x.b.getPriority()));
         int[] var15 = new int[CodeMatchPriority.values().length];
         int var16 = 0;

         for(int var9 = 0; var9 < var6.size(); ++var9) {
            AIUtils.a().a(var3, "examples of");
            Collection var10 = ((IRfNamedElement)((MatchAndPriority)var6.get(var9)).a).getDeclarations();
            boolean var11 = false;

            for(IRfDefElement var12 : var10) {
               if (var12 != null) {
                  String var14 = AIExpansionUtils.INSTANCE.expandDefElement(var12, (DVTEditor)null, true);
                  if (!var14.isEmpty()) {
                     var11 = true;
                     var4.add(new CodeAndLineRange(var14, (String)null, 0, 0));
                     ++var15[((MatchAndPriority)var6.get(var9)).b.ordinal()];
                     if (var16 >= var2) {
                        break;
                     }
                  }
               }
            }

            if (var11) {
               ++var16;
            }

            if (var16 >= var2) {
               break;
            }
         }

         String var17 = AIUtils.a().a(var16, (int[])var15);
         AIUtils.a().a(LogTarget.AI_CONSOLE, var17, LogSeverity.INFO, (String)null);
         return var4;
      }
   }

   public List a(String var1, EditorPosition var2, int var3, BooleanSupplier var4) {
      if (var1 != null && var1.startsWith("file:")) {
         return this.b(var1, var3, var4);
      } else {
         ArrayList var5 = new ArrayList();
         List var6 = this.a(var1, var2, var4);
         if (var6 == null) {
            return null;
         } else {
            AIUtils.a().a(var4, "usages of");
            List var7 = (List)var6.stream().map((var1x) -> new MatchAndPriority(var1x, this.a(var1x))).sorted((var0, var1x) -> Integer.compare(var0.b.getPriority(), var1x.b.getPriority())).collect(Collectors.toList());
            int[] var8 = new int[CodeMatchPriority.values().length];
            int var9 = 0;

            for(int var10 = 0; var10 < var7.size(); ++var10) {
               AIUtils.a().a(var4, "usages of");
               EditorRange var11 = (EditorRange)((MatchAndPriority)var7.get(var10)).a;
               if (!AIProtectManager.INSTANCE.isFileProtected(var11.b())) {
                  CodeAndLineRange var12 = AIUtils.a().a((EditorRange)var11);
                  if (var12 != null) {
                     var5.add(var12);
                     ++var9;
                     ++var8[((MatchAndPriority)var7.get(var10)).b.ordinal()];
                     if (var9 >= var3) {
                        break;
                     }
                  }
               }
            }

            String var13 = AIUtils.a().a(var9, (int[])var8);
            AIUtils.a().a(LogTarget.AI_CONSOLE, var13, LogSeverity.INFO, (String)null);
            return var5;
         }
      }
   }

   public List a(int var1, boolean var2, String var3, BooleanSupplier var4) {
      ArrayList var5 = new ArrayList();
      int var6 = 0;
      AIUtils.a().a(var4, var2 ? "recent code sections from open editors" : "recent code sections");

      for(IRfDefElement var8 : var2 ? HotspotManager.a().d() : HotspotManager.a().c()) {
         AIUtils.a().a(var4, var2 ? "recent code sections from open editors" : "recent code sections");
         if (var3 == null || !AIUtils.a().e(var3).equals(AIUtils.a().e(AIUtils.a().b((IRfDefElement)var8)))) {
            String var10 = AIExpansionUtils.INSTANCE.expandDefElement(var8, (DVTEditor)null, true);
            if (!var10.isEmpty()) {
               var5.add(new CodeAndLineRange(var10, (String)null, 0, 0));
               ++var6;
               if (var6 >= var1) {
                  break;
               }
            }
         }
      }

      return var5;
   }

   public List a(DVTEditor var1, boolean var2, int var3, int var4, BooleanSupplier var5) {
      if (var2 && !AbstractBreadcrumbUtilsCommon.b(var1)) {
         throw new AINoDHAvailableException(new AIExceptionData(AIExceptionKind.DH_NOT_AVAILABLE.KIND, AIExceptionKind.DH_NOT_AVAILABLE.MESSAGE, 1));
      } else if (!var2 && !AbstractBreadcrumbUtilsCommon.a(var1)) {
         throw new AINoVHAvailableException(new AIExceptionData(AIExceptionKind.VH_NOT_AVAILABLE.KIND, AIExceptionKind.VH_NOT_AVAILABLE.MESSAGE, 1));
      } else {
         List var6 = var2 ? this.a(var1, var3, var4, var5) : this.b(var1, var3, var4, var5);
         AIUtils.a().a(var5, var2 ? "design hierarchy" : "verification hierarchy");
         if (var6 == null) {
            throw var2 ? new AINoDHAvailableException(new AIExceptionData(AIExceptionKind.DH_NOT_AVAILABLE.KIND, AIExceptionKind.DH_NOT_AVAILABLE.MESSAGE, 1)) : new AINoVHAvailableException(new AIExceptionData(AIExceptionKind.VH_NOT_AVAILABLE.KIND, AIExceptionKind.VH_NOT_AVAILABLE.MESSAGE, 1));
         } else {
            return var6;
         }
      }
   }

   public List a(String var1, int var2, BooleanSupplier var3) {
      List var4 = AISymbolSolver.INSTANCE.solveSymbol(var1, var2 > 0 ? var2 : Integer.MAX_VALUE, var3);
      AIUtils.a().a(var3, "symbol");
      return (List)var4.stream().map((var0) -> new CodeAndLineRange(var0, (String)null, 0, 0)).collect(Collectors.toList());
   }

   public List a(EditorPosition var1, DependenciesExpansionStrategy var2, int var3, int var4, int var5, BooleanSupplier var6) {
      if (var1 == null) {
         return null;
      } else {
         int var7 = AIProtectManager.INSTANCE.getProtectLineNumber(var1.c());
         if (var7 > 0) {
            AIUtils.a().b(var1.c(), var7);
         }

         IRfNamedElementAndScope var8 = AIUtils.a().a(var1);
         if (var8 == null) {
            return null;
         } else {
            LanguageKind var9 = var8.getLanguageKind();
            IAIContributor var10 = AIContributorManager.INSTANCE.getContributor(var9);
            if (var10 == null) {
               return null;
            } else {
               IRfDefElement var11 = AIUtils.a().a(var8, var10.b(), true);
               if (var11 == null) {
                  return null;
               } else {
                  AIUtils.a().a(var6, "dependencies of");
                  List var12 = this.a(var11, var1, var9, var6);
                  return var12 == null ? null : this.a(var12, var2, var3, var4, var5, var6);
               }
            }
         }
      }
   }

   public List a(String var1, DependenciesExpansionStrategy var2, int var3, int var4, int var5, BooleanSupplier var6) {
      boolean var7 = !var1.contains("*");
      LinkedHashSet var8 = new LinkedHashSet();
      if (var1.startsWith("file:")) {
         return null;
      } else {
         for(IRfNamedElement var10 : AISymbolSolver.INSTANCE.getNamedElementsByName(var1)) {
            AIUtils.a().a(var6, "dependencies of");
            if (!(var10 instanceof IMacroInfo)) {
               if (var10 instanceof IRfTypeAliasElement) {
                  var10 = ((IRfTypeAliasElement)var10).getTranslatedType();
               }

               String var12 = AIUtils.a().b((IRfNamedElement)var10);
               if (var12 != null) {
                  int var13 = AIProtectManager.INSTANCE.getProtectLineNumber(var12);
                  if (var13 > 0) {
                     if (var7) {
                        AIUtils.a().b(var12, var13);
                     }
                  } else {
                     List var14 = this.a(var10.getDeclaration(), AIUtils.a().e(var10.getDeclaration()), var10.getLanguageKind(), var6);
                     if (var14 != null) {
                        var8.addAll(var14);
                     }
                  }
               }
            }
         }

         return this.a((List)(new ArrayList(var8)), var2, var3, var4, var5, var6);
      }
   }

   private List a(IRfDefElement var1, EditorPosition var2, LanguageKind var3, BooleanSupplier var4) {
      if (var3 != null && var1 != null) {
         IAIContributor var5 = AIContributorManager.INSTANCE.getContributor(var3);
         return var5 == null ? null : var5.a(var1, var2, var4);
      } else {
         return null;
      }
   }

   private List a(DVTEditor var1, int var2, int var3, BooleanSupplier var4) {
      IProject var5 = AIUtils.a().d();
      if (var5 == null) {
         return null;
      } else {
         BreadcrumbInput var6 = DHBreadcrumbUtils.b(DHBreadcrumbViewer.class, var1);
         if (var6 == null) {
            return null;
         } else {
            List var7 = var6.a();
            if (var7 != null && var7.size() >= 2) {
               AIUtils.a().a(var4, "design hierarchy");
               List var8 = var7.subList(1, var7.size());
               List var9 = (List)var8.stream().map((var1x) -> var1x.a(var5)).collect(Collectors.toList());
               IRfBreadcrumbElement var10 = ((BreadcrumbSegment)var8.get(var8.size() - 1)).a();
               if (!(var10 instanceof ElementPath)) {
                  return null;
               } else {
                  ElementPath var11 = ((ElementPath)var10).toElaborationForm();
                  List var12 = AIUtils.a().a((ElementPath)var11);
                  if (var12 == null) {
                     return null;
                  } else {
                     AIUtils.a().a(var4, "design hierarchy");
                     int var13 = var12.size();
                     int var14 = var13 + var3;
                     LazyElDesignHierarchyModel var15 = new LazyElDesignHierarchyModel(var5);
                     TreeElement var16 = this.a(new LinkedList(var12), var13, var14, var15, (String)null, var4);
                     if (var13 == 1) {
                        return Arrays.asList(var16);
                     } else {
                        AIUtils.a().a(var4, "design hierarchy");
                        ArrayList var17 = new ArrayList();

                        for(int var18 = 0; var18 < var13 - 1; ++var18) {
                           var17.add(new TreeElement());
                        }

                        for(int var26 = 0; var26 < var13 - 1; ++var26) {
                           ((TreeElement)var17.get(var26)).a(var26 == var13 - 2 ? var16 : (TreeElement)var17.get(var26 + 1));
                           ((TreeElement)var17.get(var26)).a(var26 > 0 ? this.a((String)var12.get(var26), this.a(var9.get(var26))) : (String)var12.get(var26));
                        }

                        var16.a(this.a((String)var12.get(var13 - 1), this.a(var9.get(var13 - 1))));
                        if (var2 == 0) {
                           return Arrays.asList((TreeElement)var17.get(0));
                        } else {
                           for(int var27 = Math.max(var13 - var2 - 1, 0); var27 < var13 - 1; ++var27) {
                              AIUtils.a().a(var4, "design hierarchy");
                              TreeElement var19 = (TreeElement)var17.get(var27);
                              String[] var20 = (String[])var12.subList(0, var27 + 1).toArray(new String[0]);
                              String var21 = (String)var12.get(var27 + 1);
                              List var22 = var15.a(TreePath.a((TreePath)null, var20, (int[])null));
                              if (var22 != null && !var22.isEmpty()) {
                                 for(TreePath var23 : var22) {
                                    if (!var21.equals(var23.l())) {
                                       TreeElement var25 = new TreeElement();
                                       var25.a(this.a(var23.l(), this.a(var23.h())));
                                       var19.a(var25);
                                    }
                                 }
                              }
                           }

                           return Arrays.asList((TreeElement)var17.get(0));
                        }
                     }
                  }
               }
            } else {
               return null;
            }
         }
      }
   }

   private TreeElement a(LinkedList var1, int var2, int var3, LazyElDesignHierarchyModel var4, String var5, BooleanSupplier var6) {
      TreeElement var7 = new TreeElement(this.a((String)var1.getLast(), var5), (List)null);
      AIUtils.a().a(var6, "design hierarchy");
      List var8 = var4.a(TreePath.a((TreePath)null, (String[])var1.toArray(new String[0]), (int[])null));
      if (var8 != null && !var8.isEmpty() && var2 < var3) {
         for(TreePath var9 : var8) {
            AIUtils.a().a(var6, "design hierarchy");
            var1.addLast(var9.l());
            String var11 = this.a(var9.h());
            TreeElement var12 = this.a(var1, var2 + 1, var3, var4, var11, var6);
            var7.a(var12);
            var1.removeLast();
         }
      }

      return var7;
   }

   private List b(DVTEditor var1, int var2, int var3, BooleanSupplier var4) {
      BreadcrumbInput var5 = VHBreadcrumbUtils.b(VHBreadcrumbViewer.class, var1);
      if (var5 == null) {
         return null;
      } else {
         LanguageKind var6 = var1.l();
         IAIContributor var7 = AIContributorManager.INSTANCE.getContributor(var6);
         if (var7 == null) {
            return null;
         } else {
            ILabelProvider var8 = var7.a(var1);
            if (var8 == null) {
               return null;
            } else {
               List var9 = var5.a();
               if (var9 != null && !var9.isEmpty()) {
                  AIUtils.a().a(var4, "verification hierarchy");
                  ArrayList var10 = new ArrayList();

                  for(BreadcrumbSegment var11 : var9) {
                     IRfBreadcrumbElement var13 = var11.a();
                     if (!(var13 instanceof IRfVHBreadcrumbElement)) {
                        return null;
                     }

                     var10.add((IRfVHBreadcrumbElement)var13);
                  }

                  int var21 = var10.size();
                  int var22 = var21 + var3;
                  TreeElement var23 = this.a((IRfVHBreadcrumbElement)var10.get(var21 - 1), var8, var21, var22, var4);
                  if (var21 == 1) {
                     return Arrays.asList(var23);
                  } else {
                     AIUtils.a().a(var4, "verification hierarchy");
                     ArrayList var14 = new ArrayList();

                     for(int var15 = 0; var15 < var21 - 1; ++var15) {
                        var14.add(new TreeElement());
                     }

                     for(int var24 = 0; var24 < var21 - 1; ++var24) {
                        ((TreeElement)var14.get(var24)).a(var24 == var21 - 2 ? var23 : (TreeElement)var14.get(var24 + 1));
                        ((TreeElement)var14.get(var24)).a(var8.getText(var10.get(var24)));
                     }

                     if (var2 == 0) {
                        return Arrays.asList((TreeElement)var14.get(0));
                     } else {
                        for(int var25 = Math.max(var21 - var2 - 1, 0); var25 < var21 - 1; ++var25) {
                           AIUtils.a().a(var4, "verification hierarchy");
                           List var16 = ((IRfVHBreadcrumbElement)var10.get(var25)).getAllVHChildren();
                           if (var16 != null && !var16.isEmpty()) {
                              String var17 = var8.getText(var10.get(var25 + 1));

                              for(IRfVHBreadcrumbElement var18 : var16) {
                                 String var20 = var8.getText(var18);
                                 if (!var20.equals(var17)) {
                                    ((TreeElement)var14.get(var25)).a(new TreeElement(var20, (List)null));
                                 }
                              }
                           }
                        }

                        return Arrays.asList((TreeElement)var14.get(0));
                     }
                  }
               } else {
                  return null;
               }
            }
         }
      }
   }

   private TreeElement a(IRfVHBreadcrumbElement var1, ILabelProvider var2, int var3, int var4, BooleanSupplier var5) {
      TreeElement var6 = new TreeElement(var2.getText(var1), (List)null);
      AIUtils.a().a(var5, "verification hierarchy");
      List var7 = var1.getAllVHChildren();
      if (var7 != null && !var7.isEmpty() && var3 < var4) {
         for(IRfVHBreadcrumbElement var8 : var7) {
            AIUtils.a().a(var5, "verification hierarchy");
            TreeElement var10 = this.a(var8, var2, var3 + 1, var4, var5);
            var6.a(var10);
         }
      }

      return var6;
   }

   private List a(String var1, EditorPosition var2, BooleanSupplier var3) {
      if ((var1 == null || var1.isEmpty() || var2 == null) && (var1 != null && !var1.isEmpty() || var2 != null)) {
         Object var4 = new ArrayList();
         LinkedHashSet var5 = new LinkedHashSet();
         AIUtils.a().a(var3, "usages of");
         if (var2 != null) {
            IRfNamedElementAndScope var6 = AIUtils.a().a(var2);
            if (var6 == null) {
               return null;
            }

            IRfNamedElement var7 = var6.getIRfNamedElement();
            if (var7 != null) {
               ((List)var4).add(var7);
            } else {
               LanguageKind var8 = var6.getLanguageKind();
               IAIContributor var9 = AIContributorManager.INSTANCE.getContributor(var8);
               if (var9 == null) {
                  return null;
               }

               IRfDefElement var10 = AIUtils.a().a(var6, var9.a(SnippetSelectionType.CONTAINER), true);
               if (var10 == null) {
                  return null;
               }

               var7 = var10.getNamedElement();
               if (var7 == null) {
                  return null;
               }

               ((List)var4).add(var7);
            }
         } else {
            var4 = AISymbolSolver.INSTANCE.getNamedElementsByName(var1);
         }

         for(IRfNamedElement var11 : var4) {
            AIUtils.a().a(var3, "usages of");
            IRfSingleLangProject var14 = var11.getRfProject();
            if (var14 != null) {
               LanguageKind var15 = var11.getLanguageKind();
               IAIContributor var16 = AIContributorManager.INSTANCE.getContributor(var15);
               if (var16 != null) {
                  var16.a((Set)var5, (IRfNamedElement)var11, (IProject)var14.getProject());
               }
            }
         }

         return new ArrayList(var5);
      } else {
         return null;
      }
   }

   private List b(String var1, int var2, BooleanSupplier var3) {
      IProject var4 = AIUtils.a().d();
      if (var4 == null) {
         return null;
      } else {
         IRfSingleLangProject var5 = RfMixedLangManager.getInstance().getRfSingleLangProject(var4, "ro.amiq.vlogdt.VlogNature", false);
         if (var5 == null) {
            return null;
         } else {
            ArrayList var6 = new ArrayList();
            HashSet var7 = new HashSet();

            for(String var9 : AISymbolSolver.INSTANCE.getAbsolutePathsFromFileQuery(var1)) {
               AIUtils.a().a(var3, "usages of");
               Set var11 = var5.getIncludingFilesForFilePath(var9);
               if (var11 != null && !var11.isEmpty()) {
                  for(String var12 : var11) {
                     if (!AIProtectManager.INSTANCE.isFileProtected(var12) && !var7.contains(var12)) {
                        CodeAndLineRange var14 = AIUtils.a().d(var12);
                        if (var14 != null) {
                           var7.add(var12);
                           var6.add(var14);
                           if (var6.size() >= var2) {
                              return var6;
                           }
                        }
                     }
                  }
               }
            }

            return var6;
         }
      }
   }

   private List b(String var1) {
      IDVTElementWrapper var2 = this.a(var1);
      if (var2 != null && var2.getChildren() != null && !var2.getChildren().isEmpty()) {
         List var3 = var2.getChildren();
         ArrayList var4 = new ArrayList();

         for(IDVTElementWrapper var5 : var3) {
            var4.add(this.a(var5));
         }

         return var4;
      } else {
         return null;
      }
   }

   private TreeElement a(IDVTElementWrapper var1) {
      if (var1 == null) {
         return null;
      } else {
         Object var2 = var1.getRfElement();
         if (!(var2 instanceof IRfDefElement)) {
            return null;
         } else {
            IRfDefElement var3 = (IRfDefElement)var2;
            TreeElement var4 = new TreeElement();
            String var5 = AIUtils.a().d(var3);
            if (var5 == null) {
               return null;
            } else {
               var4.a(var5);
               List var6 = var1.getChildren();
               if (var6 == null) {
                  return var4;
               } else {
                  for(IDVTElementWrapper var7 : var6) {
                     var4.a(this.a(var7));
                  }

                  return var4;
               }
            }
         }
      }
   }

   private CodeMatchPriority a(EditorRange var1) {
      String var2 = AIUtils.a().e(var1.b());
      if (var2.equals(this.b())) {
         return CodeMatchPriority.ACTIVE_EDITOR;
      } else {
         Set var3 = HotspotManager.a().b();
         if (var3 != null && var3.contains(var2)) {
            return CodeMatchPriority.OPEN_FILES;
         } else {
            return HotspotManager.a().b(var2) ? CodeMatchPriority.RECENT_FILES : CodeMatchPriority.GENERIC_MATCH;
         }
      }
   }

   private String a(Object var1) {
      if (var1 instanceof IRfInstanceElement) {
         return ((IRfInstanceElement)var1).getAssociatedTypeName();
      } else {
         return var1 instanceof IRfNamedElement ? ((IRfNamedElement)var1).getName() : null;
      }
   }

   private String a(String var1, String var2) {
      return var2 == null ? var1 : var1 + ": " + var2;
   }

   private List a(List var1, DependenciesExpansionStrategy var2, int var3, int var4, int var5, BooleanSupplier var6) {
      var1 = (List)var1.stream().filter((var0) -> !AIProtectManager.INSTANCE.isDefElementProtected(var0.e())).collect(Collectors.toList());
      if (var2 == DependenciesExpansionStrategy.FORCE_CODE_SNIPPET) {
         return this.a(var1, var3, var4, var5, var6);
      } else if (var2 == DependenciesExpansionStrategy.FORCE_OUTLINE) {
         return this.b(var1, var3, var4, var5, var6);
      } else {
         int var7 = 0;
         ArrayList var8 = new ArrayList();

         for(DependencySource var9 : var1) {
            if (var9.a()) {
               String var11 = var9.b() ? var9.c() : var9.d();
               var8.add(new Dependency(false, (MultiRootTree)null, new CodeAndLineRange(var11, (String)null, 0, 0)));
               var7 += var11.length();
            } else if (var9.b()) {
               IDVTElementWrapper var25 = this.a(var9.e());
               TreeElement var12 = this.a(var25);
               var8.add(new Dependency(true, new MultiRootTree(Arrays.asList(var12)), (CodeAndLineRange)null));
               var7 += AIUtils.a().a((TreeElement)var12);
            } else {
               AIUtils.a().a(var6, "dependencies of");
               String var26 = var9.a() ? var9.d() : AIExpansionUtils.INSTANCE.expandDefElement(var9.e(), (DVTEditor)null, false).trim();
               var8.add(new Dependency(false, (MultiRootTree)null, new CodeAndLineRange(var26, (String)null, 0, 0)));
               var7 += var26.length();
            }
         }

         var7 += var8.size() * var3 + (var8.size() - 1) * var4;
         if (var7 <= var5) {
            return var8;
         } else {
            for(int var21 = var8.size() - 1; var21 >= 0; --var21) {
               AIUtils.a().a(var6, "dependencies of");
               DependencySource var23 = (DependencySource)var1.get(var21);
               if (!var23.a() && !((Dependency)var8.get(var21)).a()) {
                  IRfDefElement var27 = var23.e();
                  IRfNamedElement var29 = var27.getNamedElement();
                  if (!(var29 instanceof IRfCompositeType) && !(var29 instanceof IRfEnumElement)) {
                     IDVTElementWrapper var13 = this.a(var27);
                     TreeElement var14 = this.a(var13);
                     int var15 = AIUtils.a().a((TreeElement)var14);
                     int var16 = ((Dependency)var8.get(var21)).c().a().length();
                     if (var15 <= var16) {
                        var8.set(var21, new Dependency(true, new MultiRootTree(Arrays.asList(var14)), (CodeAndLineRange)null));
                        var7 -= var16;
                        var7 += var15;
                        if (var7 <= var5) {
                           return var8;
                        }
                     }
                  }
               }
            }

            ArrayList var22 = new ArrayList();
            var7 = 0;

            for(int var24 = 0; var24 < var8.size(); ++var24) {
               AIUtils.a().a(var6, "dependencies of");
               int var28 = ((Dependency)var8.get(var24)).d() + var3;
               if (var24 != 0) {
                  var28 += var4;
               }

               if (var7 + var28 > var5) {
                  break;
               }

               var7 += var28;
               var22.add((Dependency)var8.get(var24));
            }

            return var22;
         }
      }
   }

   private List a(List var1, int var2, int var3, int var4, BooleanSupplier var5) {
      ArrayList var6 = new ArrayList();
      int var7 = 0;

      for(int var8 = 0; var8 < var1.size(); ++var8) {
         AIUtils.a().a(var5, "dependencies of");
         DependencySource var9 = (DependencySource)var1.get(var8);
         String var10 = var9.a() ? var9.d() : AIExpansionUtils.INSTANCE.expandDefElement(var9.e(), (DVTEditor)null, false).trim();
         if (!var10.isEmpty()) {
            int var11 = var10.length() + var2;
            if (var8 != 0) {
               var11 += var3;
            }

            if (var7 + var11 > var4) {
               return var6;
            }

            var7 += var11;
            var6.add(new Dependency(false, (MultiRootTree)null, new CodeAndLineRange(var10, (String)null, 0, 0)));
         }
      }

      return var6;
   }

   private List b(List var1, int var2, int var3, int var4, BooleanSupplier var5) {
      ArrayList var6 = new ArrayList();
      int var7 = 0;

      for(int var8 = 0; var8 < var1.size(); ++var8) {
         AIUtils.a().a(var5, "dependencies of");
         DependencySource var9 = (DependencySource)var1.get(var8);
         if (var9.a()) {
            String var10 = var9.c();
            int var11 = var10.length() + var2;
            if (var8 != 0) {
               var11 += var3;
            }

            if (var7 + var11 > var4) {
               return var6;
            }

            var6.add(new Dependency(false, (MultiRootTree)null, new CodeAndLineRange(var10, (String)null, 0, 0)));
         } else {
            IDVTElementWrapper var13 = AISnippetSolver.a().a(var9.e());
            if (var13 != null) {
               TreeElement var14 = this.a(var13);
               if (var14 != null) {
                  int var12 = AIUtils.a().a((TreeElement)var14) + var2;
                  if (var8 != 0) {
                     var12 += var3;
                  }

                  if (var7 + var12 > var4) {
                     return var6;
                  }

                  var7 += var12;
                  var6.add(new Dependency(true, new MultiRootTree(Arrays.asList(var14)), (CodeAndLineRange)null));
               }
            }
         }
      }

      return var6;
   }

   protected abstract String b();

   protected abstract IDVTElementWrapper a(String var1);

   protected abstract IDVTElementWrapper a(IRfDefElement var1);

   // $FF: synthetic method
   static int[] d() {
      int[] var10000 = b;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[SnippetOutlineOfSelectedType.values().length];

         try {
            var0[SnippetOutlineOfSelectedType.CONTAINER.ordinal()] = 3;
         } catch (NoSuchFieldError var3) {
         }

         try {
            var0[SnippetOutlineOfSelectedType.ELEMENT.ordinal()] = 2;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[SnippetOutlineOfSelectedType.FILE.ordinal()] = 1;
         } catch (NoSuchFieldError var1) {
         }

         b = var0;
         return var0;
      }
   }

   private static class MatchAndPriority {
      public Object a;
      public CodeMatchPriority b;

      public MatchAndPriority(Object var1, CodeMatchPriority var2) {
         this.a = var1;
         this.b = var2;
      }
   }
}
