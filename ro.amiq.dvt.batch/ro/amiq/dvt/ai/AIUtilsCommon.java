package ro.amiq.dvt.ai;

import java.io.File;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BooleanSupplier;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.eclipse.core.filebuffers.FileBuffers;
import org.eclipse.core.filebuffers.ITextFileBuffer;
import org.eclipse.core.filebuffers.ITextFileBufferManager;
import org.eclipse.core.filebuffers.LocationKind;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;
import org.eclipse.jface.text.IDocument;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.contributor.AIContributorManager;
import ro.amiq.dvt.ai.contributor.IAIContributor;
import ro.amiq.dvt.ai.model.CodeAndLineRange;
import ro.amiq.dvt.ai.model.CodeMatchPriority;
import ro.amiq.dvt.ai.model.EditorPosition;
import ro.amiq.dvt.ai.model.EditorRange;
import ro.amiq.dvt.ai.model.LogSeverity;
import ro.amiq.dvt.ai.model.LogTarget;
import ro.amiq.dvt.ai.model.SnippetSelectionType;
import ro.amiq.dvt.ai.model.TreeElement;
import ro.amiq.dvt.ai.model.exceptions.AIExceptionData;
import ro.amiq.dvt.ai.model.exceptions.AIExceptionKind;
import ro.amiq.dvt.ai.model.exceptions.AIProtectException;
import ro.amiq.dvt.ai.model.exceptions.AIProtectExceptionData;
import ro.amiq.dvt.ai.model.exceptions.AIUnexpectedBinaryFileException;
import ro.amiq.dvt.model.reflection.ElementPath;
import ro.amiq.dvt.model.reflection.IRfActionBlockElement;
import ro.amiq.dvt.model.reflection.IRfAssociatedTypeElement;
import ro.amiq.dvt.model.reflection.IRfCompositeType;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfFieldElement;
import ro.amiq.dvt.model.reflection.IRfFileDef;
import ro.amiq.dvt.model.reflection.IRfInstanceElement;
import ro.amiq.dvt.model.reflection.IRfListType;
import ro.amiq.dvt.model.reflection.IRfNamedElement;
import ro.amiq.dvt.model.reflection.IRfNamedElementAndScope;
import ro.amiq.dvt.model.reflection.IRfScopeElement;
import ro.amiq.dvt.model.reflection.IRfTypeAliasElement;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.startup.core.DVTLogger;
import ro.amiq.dvt.ui.editor.DVTEditor;
import ro.amiq.dvt.ui.editor.TextUtils;
import ro.amiq.dvt.ui.views.symbolcollector.ISymbolCollectorView;
import ro.amiq.dvt.utils.DVTFileUtils;
import ro.amiq.dvt.utils.TextFileBufferInfo;

public abstract class AIUtilsCommon {
   private static final String e = "rand ";
   private static final String f = "randc ";
   private static final String g = "field ";
   private static final String h = "enum";
   private static final String i = "struct";
   private static final String j = "union";
   private static final String k = "packed";
   protected static final String a = "...";
   protected static final int b = 4;
   private static final Pattern l = Pattern.compile("\\[\\D+\\]");
   private static final Pattern m = Pattern.compile(":\\d+-\\d+$");
   public static final String c = Paths.get(".dvt", "ai", "protect").toString();
   public static final String d = "Edit";
   private static final int n = 10000;
   private static final double o = (double)2.5F;

   public IRfDefElement a(IRfNamedElementAndScope var1, Set var2, boolean var3) {
      if (var1 == null) {
         return null;
      } else if (var2 != null && !var2.isEmpty()) {
         IRfDefElement var4 = null;
         if (var3) {
            IRfNamedElement var5 = var1.getIRfNamedElement();
            var4 = this.a(var5);
            if (this.a((Object)var5, var2, true)) {
               return var4;
            }
         }

         for(IRfScopeElement var6 = var1.getScope(); var6 != null; var6 = var6.getEnclosingScope()) {
            if (var6 instanceof IRfNamedElement && this.a((Object)var6, var2, false)) {
               return this.a((IRfNamedElement)var6);
            }

            if (var6 instanceof IRfDefElement) {
               if (this.a((Object)var6, var2, false)) {
                  return (IRfDefElement)var6;
               }

               if (this.a((Object)((IRfDefElement)var6).getNamedElement(), var2, false)) {
                  return this.a(((IRfDefElement)var6).getNamedElement());
               }
            }
         }

         return var4;
      } else {
         return null;
      }
   }

   public IRfDefElement a(IRfNamedElement var1) {
      if (var1 == null) {
         return null;
      } else {
         Object var2 = var1.getImplementation();
         return var2 instanceof IRfDefElement ? (IRfDefElement)var2 : var1.getDeclaration();
      }
   }

   private boolean a(Object var1, Set var2, boolean var3) {
      for(Class var4 : var2) {
         if (var4.isInstance(var1) && (!(var1 instanceof IRfInstanceElement) || var3)) {
            return true;
         }
      }

      return false;
   }

   public String b(IRfNamedElement var1) {
      return var1 == null ? null : this.b(var1.getDeclaration());
   }

   public String b(IRfDefElement var1) {
      if (var1 == null) {
         return null;
      } else {
         IRfFileDef var2 = var1.getDefFile();
         if (var2 == null) {
            return null;
         } else {
            ParserPath var3 = var2.getParserPath();
            return var3 == null ? null : var3.path;
         }
      }
   }

   public IRfDefElement c(EditorPosition var1) {
      IRfNamedElementAndScope var2 = AIUtils.a().a(var1);
      if (var2 == null) {
         return null;
      } else {
         LanguageKind var3 = var2.getLanguageKind();
         IAIContributor var4 = AIContributorManager.INSTANCE.getContributor(var3);
         if (var4 == null) {
            return null;
         } else {
            IRfDefElement var5 = AIUtils.a().a(var2, var4.a(SnippetSelectionType.ELEMENT), true);
            if (var5 == null) {
               return null;
            } else {
               IRfNamedElement var6 = var5.getNamedElement();
               if (var6 instanceof IRfTypeAliasElement) {
                  IRfNamedElement var7 = ((IRfTypeAliasElement)var6).getTranslatedType();
                  if (var7 != null) {
                     var5 = var7.getDeclaration();
                  }
               }

               return var5;
            }
         }
      }
   }

   public IRfDefElement c(IRfDefElement var1) {
      IRfScopeElement var2 = var1.getNamedElement().getEnclosingScope();
      if (!(var2 instanceof IRfNamedElement)) {
         return null;
      } else {
         for(IRfDefElement var3 : ((IRfNamedElement)var2).getDeclarations()) {
            if (var3.getStartOffset() <= var1.getStartOffset() && var3.getEndOffset() >= var1.getEndOffset() && var1.getDefFile().equals(var3.getDefFile())) {
               return var3;
            }
         }

         return null;
      }
   }

   public String a(int var1, int[] var2) {
      StringBuilder var3 = new StringBuilder("Found " + var1 + " code matches: ");

      for(int var4 = 0; var4 < var2.length; ++var4) {
         if (var2[var4] > 0) {
            var3.append(var2[var4]).append(" ").append(CodeMatchPriority.values()[var4].name()).append(" ");
         }
      }

      return var3.toString();
   }

   public String d(IRfDefElement var1) {
      if (var1 == null) {
         return "";
      } else {
         IRfNamedElement var2 = var1.getNamedElement();
         if (var2 instanceof IRfActionBlockElement) {
            return null;
         } else {
            if (var2 instanceof IRfTypeAliasElement) {
               var2 = ((IRfTypeAliasElement)var2).getTranslatedType();
            }

            if (var2 instanceof IRfCompositeType) {
               return this.a((Object)var2);
            } else {
               if (var2 != null) {
                  String var3 = var2.getSignature();
                  if (var2 instanceof IRfFieldElement && var3 != null && var3.startsWith("field ") && (var2.isRand() || var2.isRandc())) {
                     String var4 = var2.isRand() ? "rand " : "randc ";
                     var3 = "field " + var4 + var3.substring("field ".length());
                  }

                  if (var3 != null) {
                     String[] var10 = var3.split("\n");
                     StringBuilder var5 = new StringBuilder();

                     for(String var6 : var10) {
                        if (!l.matcher(var6).matches()) {
                           var5.append(var6);
                        }
                     }

                     return var5.toString();
                  }
               }

               return var1.getName();
            }
         }
      }
   }

   private String a(Object var1) {
      if (var1 == null) {
         return "";
      } else {
         StringBuilder var2 = new StringBuilder();
         String var3 = ((IRfNamedElement)var1).printScope();
         if (var3 == null) {
            return "";
         } else {
            IRfCompositeType var4 = (IRfCompositeType)var1;
            if (var4.isStruct()) {
               var2.append("struct").append(' ');
            } else if (var4.isUnion()) {
               var2.append("union").append(' ');
            } else if (var4.isEnum()) {
               var2.append("enum").append(' ');
            }

            if (var4.isPacked()) {
               var2.append("packed").append(' ');
            }

            if (var3.endsWith(".")) {
               var3 = var3.substring(0, var3.length() - 1);
            }

            var2.append(var3);
            return var2.toString();
         }
      }
   }

   public CodeAndLineRange d(String var1) {
      IDocument var2 = this.a(var1);
      return var2 == null ? null : new CodeAndLineRange(AIExpansionUtils.INSTANCE.indentCodeSnippet(var2.get(), var1), var1, 1, var2.getNumberOfLines());
   }

   public CodeAndLineRange a(EditorRange var1) {
      if (var1 == null) {
         return null;
      } else {
         ITextFileBufferManager var2 = null;
         LocationKind var3 = null;
         IPath var4 = null;
         boolean var5 = false;

         try {
            IFile var6 = var1.a();
            IDocument var7 = DVTFileUtils.a().c(var6);
            if (var7 == null) {
               var2 = FileBuffers.getTextFileBufferManager();
               TextFileBufferInfo var8 = DVTFileUtils.a().f(var6);
               var4 = var8.a();
               var3 = var8.b();
               ITextFileBuffer var9 = var2.getTextFileBuffer(var4, var3);
               if (var9 == null) {
                  var5 = true;
                  var2.connect(var4, var3, (IProgressMonitor)null);
               }

               var9 = var2.getTextFileBuffer(var4, var3);
               var7 = var9.getDocument();
            }

            if (var7 != null) {
               IRfNamedElementAndScope var39 = this.a(new EditorPosition(var6, var1.c()));
               if (var39 == null) {
                  return null;
               }

               LanguageKind var41 = var39.getLanguageKind();
               IAIContributor var10 = AIContributorManager.INSTANCE.getContributor(var41);
               if (var10 == null) {
                  return null;
               }

               IRfDefElement var11 = this.a(var39, var10.a(SnippetSelectionType.CONTAINER), false);
               if (var11 == null) {
                  return null;
               }

               String var12 = AIExpansionUtils.INSTANCE.getImplementationOrDeclarationExpandedForElement(var11, (DVTEditor)null);
               if (var12 == null) {
                  return null;
               }

               StringBuilder var13 = new StringBuilder();
               String var14 = TextUtils.a(true, this.b(var1.b()));
               String[] var15 = var12.split("\n");
               String var16 = var15[0];
               var13.append(var16.trim()).append("\n");
               var13.append(var14).append("...").append("\n");
               int var17 = DVTFileUtils.a().a(var6, var1.c()) - 1;
               int var18 = DVTFileUtils.a().a(var6, var1.d());
               int var19 = DVTFileUtils.a().c(var6, var17);
               int var20 = DVTFileUtils.a().c(var6, var18);
               String var21 = var7.get(var19, var20 - var19);
               if (var21 == null) {
                  return null;
               }

               if (!var21.trim().equals(var16.trim())) {
                  var13.append(var14).append(var21.trim()).append("\n");
                  var13.append(var14).append("...").append("\n");
               }

               var13.append(var15[var15.length - 1].trim()).append("\n");
               CodeAndLineRange var23 = new CodeAndLineRange(var13.toString(), var1.b(), var17 + 1, var18 + 1);
               return var23;
            }
         } catch (Exception var37) {
            DVTLogger.INSTANCE.logError((Throwable)var37);
            return null;
         } finally {
            if (var2 != null && var5) {
               try {
                  var2.disconnect(var4, var3, (IProgressMonitor)null);
               } catch (Exception var36) {
                  DVTLogger.INSTANCE.logError((Throwable)var36);
               }
            }

         }

         return null;
      }
   }

   public List a(ElementPath var1) {
      if (var1 == null) {
         return null;
      } else {
         String[] var2 = var1.getSegments();
         return var2 != null && var2.length != 0 ? Arrays.asList(var2) : null;
      }
   }

   public String e(String var1) {
      if (var1 == null) {
         return "";
      } else {
         IPath var2 = Path.fromOSString(var1);
         String var3 = var2.getDevice();
         if (var3 != null && !var3.isEmpty()) {
            var2 = var2.setDevice(var3.toUpperCase());
         }

         return var2.toOSString();
      }
   }

   public String a(List var1) {
      StringBuilder var2 = new StringBuilder();
      List var3 = (List)var1.stream().map((var0) -> "'" + var0 + "'").collect(Collectors.toList());
      var2.append("Can not resolve environment ");
      var2.append(var1.size() == 1 ? "variable " : "variables ");

      for(int var4 = 0; var4 < var1.size() - 1; ++var4) {
         var2.append((String)var3.get(var4) + ", ");
      }

      var2.append((String)var3.get(var1.size() - 1));
      var2.append(" in AI Assistant protect file!");
      return var2.toString();
   }

   public boolean f(String var1) {
      return m.matcher(var1).find();
   }

   public void b(String var1, int var2) {
      String var3 = Paths.get(var1).getFileName().toString();
      String var4 = String.format(AIExceptionKind.PROTECTED_FILE_ACCESS.MESSAGE, var3);
      throw new AIProtectException(new AIProtectExceptionData(AIExceptionKind.PROTECTED_FILE_ACCESS.KIND, var4, 0, var2));
   }

   public void g(String var1) {
      String var2 = Paths.get(var1).getFileName().toString();
      String var3 = String.format(AIExceptionKind.UNEXPECTED_BINARY_FILE.MESSAGE, var2);
      throw new AIUnexpectedBinaryFileException(new AIExceptionData(AIExceptionKind.UNEXPECTED_BINARY_FILE.KIND, var3, 0));
   }

   public int a(IRfNamedElement var1, EditorPosition var2) {
      if (var1 != null && var2 != null) {
         IRfDefElement var3 = var1.getDeclaration();
         if (var3 == null) {
            return Integer.MAX_VALUE;
         } else {
            IRfFileDef var4 = var3.getDefFile();
            if (var4 == null) {
               return Integer.MAX_VALUE;
            } else {
               ParserPath var5 = var4.getParserPath();
               if (var5 != null && var5.path != null) {
                  String var6 = var2.c();
                  if (!var5.path.equals(var6)) {
                     return Integer.MAX_VALUE;
                  } else {
                     int var7 = var2.b();
                     int var8 = var3.getStartOffset();
                     return Math.abs(var7 - var8);
                  }
               } else {
                  return Integer.MAX_VALUE;
               }
            }
         }
      } else {
         return Integer.MAX_VALUE;
      }
   }

   public void a(IRfAssociatedTypeElement var1, IRfNamedElement var2, Map var3, int var4) {
      IRfNamedElement var5 = var1.getAssociatedType();
      if (var5 instanceof IRfTypeAliasElement) {
         var5 = ((IRfTypeAliasElement)var5).getTranslatedType();
      }

      if (var5 != null && !var5.checkEquals(var2)) {
         if (!var5.isPredefined() && !(var5 instanceof IRfListType)) {
            IRfDefElement var6 = var5.getDeclaration();
            if (var6 != null) {
               int var7 = (Integer)var3.getOrDefault(var6, Integer.MAX_VALUE);
               int var8 = Integer.min(var4, var7);
               var3.put(var6, var8);
            }
         }
      }
   }

   public int a(TreeElement var1) {
      return Math.max(0, this.a((TreeElement)var1, 0) - 1);
   }

   private int a(TreeElement var1, int var2) {
      if (var1 == null) {
         return 0;
      } else {
         int var3 = 0;
         List var4 = var1.b();
         if (var4 != null) {
            for(TreeElement var5 : var4) {
               var3 += this.a(var5, var2 + 1);
            }
         }

         String var7 = var1.a();
         if (var7 != null) {
            var3 += var7.length() + var2 + 1;
         }

         return var3;
      }
   }

   public boolean a(File param1) {
      // $FF: Couldn't be decompiled
   }

   public EditorPosition e(IRfDefElement var1) {
      IRfFileDef var2 = var1.getDefFile();
      if (var2 == null) {
         return null;
      } else {
         ParserPath var3 = var2.getParserPath();
         if (var3 != null && var3.path != null) {
            IFile var4 = DVTFileUtils.a().b(this.d(), var3.path);
            return var4 == null ? null : new EditorPosition(var4, var1.getStartOffset());
         } else {
            return null;
         }
      }
   }

   public abstract void a(IProgressMonitor var1);

   protected abstract void a(int var1);

   protected abstract int c();

   protected abstract ISymbolCollectorView b();

   public abstract IProject d();

   public abstract DVTEditor f();

   protected abstract IDocument a(String var1);

   public abstract IDocument a(IRfFileDef var1);

   protected abstract IRfNamedElementAndScope a(EditorPosition var1);

   protected abstract void a(LogTarget var1, String var2, LogSeverity var3, String var4);

   protected abstract int e();

   protected abstract int b(String var1);

   protected abstract String c(String var1);

   public abstract void a(String var1, int var2);

   public abstract void a(BooleanSupplier var1, String var2);

   public abstract void b(BooleanSupplier var1, String var2);
}
