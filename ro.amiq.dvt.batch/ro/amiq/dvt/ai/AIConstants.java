package ro.amiq.dvt.ai;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import ro.amiq.dvt.model.reflection.IRfAssociatedTypeElement;
import ro.amiq.dvt.model.reflection.IRfClassElement;
import ro.amiq.dvt.model.reflection.IRfCompositeType;
import ro.amiq.dvt.model.reflection.IRfDesignElement;
import ro.amiq.dvt.model.reflection.IRfEntityComplement;
import ro.amiq.dvt.model.reflection.IRfEnumElement;
import ro.amiq.dvt.model.reflection.IRfMethodElement;
import ro.amiq.dvt.model.reflection.IRfPackageElement;

public enum AIConstants {
   INSTANCE;

   public static final String LANGUAGE_SNIPPET_NAME = "language";
   public static final String SELECTED_SNIPPET_NAME = "selected";
   public static final String EXAMPLES_OF_SNIPPET_NAME = "examples of";
   public static final String USAGES_OF_SNIPPET_NAME = "usages of";
   public static final String RECENT_CODE_SECTIONS_SNIPPET_NAME = "recent code sections";
   public static final String RECENT_CODE_SECTIONS_FROM_OPEN_EDITORS_SNIPPET_NAME = "recent code sections from open editors";
   public static final String OUTLINE_OF_SNIPPET_NAME = "outline of";
   public static final String FILENAME_OF_SNIPPET_NAME = "filename of";
   public static final String SYMBOL_SNIPPET_NAME = "symbol";
   public static final String DESIGN_HIERARCHY_SNIPPET_NAME = "design hierarchy";
   public static final String VERIFICATION_HIERARCHY_SNIPPET_NAME = "verification hierarchy";
   public static final String PROBLEMS_FROM_SELECTED_SNIPPET_NAME = "problems from selected";
   public static final String DEPENDENCIES_OF_SNIPPET_NAME = "dependencies of";
   public static final String CODE_BEFORE_SELECTION_SNIPPET_NAME = "code before selection";
   public static final String CODE_AFTER_SELECTION_SNIPPET_NAME = "code after selection";
   public static final String CLIPBOARD_SNIPPET_NAME = "clipboard";
   public static final String SYMBOL_AUTOCOMPLETE = "Symbol autocomplete";
   public static final String SYMBOL_SOLVING = "Symbol solving";
   public static final Set CONTAINER_TYPES = new HashSet(Arrays.asList(IRfDesignElement.class, IRfEntityComplement.class, IRfClassElement.class, IRfPackageElement.class, IRfMethodElement.class, IRfAssociatedTypeElement.class, IRfCompositeType.class, IRfEnumElement.class));
   public static final Set CONTAINER_TYPES_WITHOUT_METHODS = new HashSet(Arrays.asList(IRfDesignElement.class, IRfEntityComplement.class, IRfClassElement.class, IRfPackageElement.class, IRfCompositeType.class, IRfEnumElement.class));
}
