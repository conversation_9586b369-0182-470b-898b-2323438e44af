package ro.amiq.dvt.ai;

import java.io.File;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.PathMatcher;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.BooleanSupplier;
import java.util.stream.Collectors;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.resources.IProject;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.contributor.AIContributorManager;
import ro.amiq.dvt.ai.contributor.IAIContributor;
import ro.amiq.dvt.ai.model.EditorRange;
import ro.amiq.dvt.ai.model.SnippetExamplesOfType;
import ro.amiq.dvt.ai.model.exceptions.AIException;
import ro.amiq.dvt.model.reflection.IMacroInfo;
import ro.amiq.dvt.model.reflection.IRfClassElement;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfNamedElement;
import ro.amiq.dvt.model.reflection.IRfSingleLangProject;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.startup.core.DVTLogger;
import ro.amiq.dvt.ui.views.DVTLabelProviderProxy;
import ro.amiq.dvt.ui.views.IDVTElementWrapper;
import ro.amiq.dvt.ui.views.lazy.views.macros.MacrosByCategoryWrapper;
import ro.amiq.dvt.ui.views.symbolcollector.DVTSymbolCollector;
import ro.amiq.dvt.ui.views.symbolcollector.DVTSymbolKind;
import ro.amiq.dvt.ui.views.symbolcollector.ISymbolCollectorPrerequisite;
import ro.amiq.dvt.ui.views.symbolcollector.SymbolCollectorSemanticSearchManager;
import ro.amiq.dvt.utils.DVTFileUtils;
import ro.amiq.dvt.utils.Utils;
import ro.amiq.dvt.utils.parser.IDVTFileInstance;

public enum AISymbolSolver {
   INSTANCE;

   private static final String a = "If(n)def Controls";
   private static final String b = "Ifndef Guards";
   private static final String c = "macro:";
   public static final String FILE_QUERY_KEY = "file:";
   public static final String SYMBOL_WILDCARD = "*";
   private static final String d = "glob:";
   private static final String e = "\\";
   private static final String f = "\\\\";
   private static final Set g = new HashSet();

   static {
      SymbolCollectorSemanticSearchManager.QUERY_KEYS_PER_LANGUAGE.values().forEach((var0) -> g.addAll(var0));
   }

   public List getSymbolsForAutocomplete(String var1) {
      ArrayList var2 = new ArrayList();
      boolean var3 = var1.startsWith("extends:");
      if (var3) {
         var1 = var1.replace("extends:", "");
      }

      Map var4 = DVTSymbolCollector.INSTANCE.collectSymbolsPrerequisites(var1, AIUtils.a().b(), true);

      for(Map.Entry var5 : var4.entrySet()) {
         DVTSymbolKind var7 = (DVTSymbolKind)var5.getKey();
         List var8 = (List)var5.getValue();
         if (var8 != null && !var8.isEmpty()) {
            DVTLabelProviderProxy var9 = var7.getLabelProvider();
            if (var7 == DVTSymbolKind.MACRO) {
               if (!var3) {
                  for(ISymbolCollectorPrerequisite var19 : var8) {
                     if (var19 instanceof MacrosByCategoryWrapper) {
                        LinkedHashMap var21 = ((MacrosByCategoryWrapper)var19).a();

                        for(Map.Entry var23 : var21.entrySet()) {
                           if (!LanguageKind.E.equals(((IRfNamedElement)var23.getKey()).getLanguageKind())) {
                              String var15 = ((IRfNamedElement)var23.getKey()).getSignature();
                              if (!"If(n)def Controls".equals(var15) && !"Ifndef Guards".equals(var15)) {
                                 List var16 = (List)var23.getValue();
                                 if (var16 != null && !var16.isEmpty()) {
                                    for(Object var17 : var16) {
                                       if (!AIProtectManager.INSTANCE.isFileProtected(this.a(var17))) {
                                          var2.add(var9.getText(var17));
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            } else {
               for(ISymbolCollectorPrerequisite var10 : var8) {
                  if (var10 instanceof IDVTElementWrapper) {
                     IDVTElementWrapper var12 = (IDVTElementWrapper)var10;
                     if (var3) {
                        IRfNamedElement var13 = (IRfNamedElement)var12.getRfElement(IRfNamedElement.class);
                        if (!(var13 instanceof IRfClassElement)) {
                           continue;
                        }
                     }

                     IAIContributor var22 = AIContributorManager.INSTANCE.getContributor(var12.getLanguageKind());
                     if (var22 != null && !var22.a(var12) && this.a(var12) && !AIProtectManager.INSTANCE.isFileProtected(this.a((Object)var12))) {
                        var2.add(var9.getText(var12));
                     }
                  }
               }
            }
         }
      }

      return var2;
   }

   public List solveSymbol(String var1, int var2, BooleanSupplier var3) {
      ArrayList var4 = new ArrayList();
      boolean var5 = !var1.contains("*") && !var1.startsWith("extends:");
      boolean var6 = var5 && !var1.startsWith("macro:");
      int var7 = 0;
      if (var6) {
         var7 = AIUtils.a().c();
      }

      AIUtils.a().b(var3, var1);

      List var15;
      try {
         if (!var1.startsWith("file:")) {
            String var8 = !this.b(var1) ? "^" + var1 + "$" : var1.substring(0, var1.indexOf(58)) + ":^" + var1.substring(var1.indexOf(58) + 1) + "$";
            if (!var5) {
               var8 = var1;
            }

            Map var9 = DVTSymbolCollector.INSTANCE.collectSymbolsPrerequisites(var8, AIUtils.a().b(), true);

            for(Map.Entry var10 : var9.entrySet()) {
               DVTSymbolKind var12 = (DVTSymbolKind)var10.getKey();
               List var13 = (List)var10.getValue();
               if (var13 != null && !var13.isEmpty()) {
                  if (var12 == DVTSymbolKind.MACRO) {
                     this.a(var13, var4, var2, var5);
                     if (var4.size() >= var2) {
                        var15 = var4;
                        return var15;
                     }
                  } else {
                     this.b(var13, var4, var2, var5);
                     if (var4.size() >= var2) {
                        var15 = var4;
                        return var15;
                     }
                  }
               }
            }

            return var4;
         }

         var15 = this.solveFileSymbol(var1, var2);
      } catch (Exception var18) {
         if (var18 instanceof AIException) {
            throw var18;
         }

         DVTLogger.INSTANCE.logError((Throwable)var18);
         return var4;
      } finally {
         if (var6) {
            AIUtils.a().a(var7);
         }

      }

      return var15;
   }

   public List getNamedElementsByName(String var1) {
      ArrayList var2 = new ArrayList();
      String var3 = !this.b(var1) ? "^" + var1 + "$" : var1.substring(0, var1.indexOf(58)) + ":^" + var1.substring(var1.indexOf(58) + 1) + "$";
      if (var1.contains("*")) {
         var3 = var1;
      }

      Map var4 = DVTSymbolCollector.INSTANCE.collectSymbolsPrerequisites(var3, AIUtils.a().b(), true);

      for(Map.Entry var5 : var4.entrySet()) {
         DVTSymbolKind var7 = (DVTSymbolKind)var5.getKey();
         List var8 = (List)var5.getValue();
         if (var8 != null && !var8.isEmpty()) {
            if (var7 == DVTSymbolKind.MACRO) {
               for(ISymbolCollectorPrerequisite var17 : var8) {
                  if (var17 instanceof MacrosByCategoryWrapper) {
                     LinkedHashMap var19 = ((MacrosByCategoryWrapper)var17).a();

                     for(Map.Entry var20 : var19.entrySet()) {
                        List var14 = (List)var20.getValue();
                        if (var14 != null && !var14.isEmpty()) {
                           for(Object var15 : var14) {
                              if (var15 instanceof IRfNamedElement) {
                                 var2.add((IRfNamedElement)var15);
                              }
                           }
                        }
                     }
                  }
               }
            } else {
               for(ISymbolCollectorPrerequisite var9 : var8) {
                  if (var9 instanceof IDVTElementWrapper) {
                     IDVTElementWrapper var11 = (IDVTElementWrapper)var9;
                     Object var12 = var11.getRfElement();
                     if (var12 instanceof IRfNamedElement) {
                        var2.add((IRfNamedElement)var12);
                     }
                  }
               }
            }
         }
      }

      return var2;
   }

   public Map getNamedElementsMatchingType(SnippetExamplesOfType var1) {
      HashMap var2 = new HashMap();
      Map var3 = DVTSymbolCollector.INSTANCE.collectSymbolsPrerequisites(var1.getValue() + ":*", AIUtils.a().b(), true);

      for(Map.Entry var4 : var3.entrySet()) {
         DVTSymbolKind var6 = (DVTSymbolKind)var4.getKey();
         if (var6 == DVTSymbolKind.TYPE) {
            List var7 = (List)var4.getValue();
            if (var7 != null && !var7.isEmpty()) {
               for(ISymbolCollectorPrerequisite var8 : var7) {
                  if (var8 instanceof IDVTElementWrapper) {
                     IDVTElementWrapper var10 = (IDVTElementWrapper)var8;
                     Object var11 = var10.getRfElement();
                     if (var11 instanceof IRfNamedElement) {
                        IRfNamedElement var12 = (IRfNamedElement)var11;
                        IRfDefElement var13 = var12.getDeclaration();
                        if (var13 != null) {
                           int var14 = var13.getStartOffset();
                           int var15 = var13.getEndOffset();
                           ParserPath var16 = var13.getParserPath();
                           if (var16 != null) {
                              IRfSingleLangProject var17 = var13.getRfProject();
                              if (var17 != null) {
                                 IProject var18 = var17.getProject();
                                 if (var18 != null) {
                                    IFile var19 = DVTFileUtils.a().b(var18, var16.path);
                                    if (var19 != null) {
                                       EditorRange var20 = new EditorRange(var19, var14, var15);
                                       var2.put(var12, var20);
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            }
         }
      }

      return var2;
   }

   public List solveFileSymbol(String var1, int var2) {
      boolean var3 = AIUtils.a().f(var1);
      int var4 = var1.lastIndexOf(58);
      boolean var5 = !var1.contains("*");
      var2 = Math.min(var2, AIUtils.a().b().g());
      ArrayList var6 = new ArrayList();
      List var7 = this.getAbsolutePathsFromFileQuery(var3 ? var1.substring(0, var4) : var1);
      var7.sort(String::compareTo);
      LinkedHashSet var8 = new LinkedHashSet(var7);

      for(String var9 : new ArrayList(var8)) {
         if (AIUtils.a().a((File)(new File(var9)))) {
            if (var5) {
               AIUtils.a().g(var9);
            }
         } else {
            int var11 = AIProtectManager.INSTANCE.getProtectLineNumber(var9);
            if (var11 > 0) {
               if (var5) {
                  AIUtils.a().b(var9, var11);
               }
            } else if (!var3) {
               var6.add(AIExpansionUtils.INSTANCE.indentCodeSnippet(Utils.a(var9, 0, Integer.MAX_VALUE), var9));
               if (var6.size() >= var2) {
                  return var6;
               }
            } else {
               String var12 = var1.substring(var4 + 1);
               String[] var13 = var12.split("-");
               int var14 = Integer.parseInt(var13[0]) - 1;
               int var15 = Integer.parseInt(var13[1]) - 1;
               var6.add(AIExpansionUtils.INSTANCE.indentCodeSnippet(Utils.a(var9, var14, var15), var9));
               if (var6.size() >= var2) {
                  return var6;
               }
            }
         }
      }

      return var6;
   }

   public List getAbsolutePathsFromFileQuery(String var1) {
      new ArrayList();
      String var3 = var1.substring("file:".length());
      List var2 = this.a(var1);
      if (!var2.isEmpty()) {
         return var2;
      } else {
         var3 = DVTFileUtils.a().h(var3);
         if (!Paths.get(var3).isAbsolute()) {
            Path var4 = Utils.g();
            if (var4 == null) {
               return var2;
            }

            var3 = Paths.get(var4.toString(), var3).toString();
         }

         var3 = DVTFileUtils.a().i(var3);
         if (!var3.contains("*")) {
            if ((new File(var3)).isFile()) {
               var2.add(var3);
            }

            return var2;
         } else {
            int var11 = var3.lastIndexOf(File.separator);
            int var5 = var3.indexOf("*");
            if (var11 > var5) {
               return var2;
            } else {
               Path var6 = Paths.get(var3.substring(0, var11));
               if (!var6.toFile().isDirectory()) {
                  return var2;
               } else {
                  if (DVTFileUtils.a().j(var3)) {
                     var3 = var3.replace("\\", "\\\\");
                  }

                  PathMatcher var7 = FileSystems.getDefault().getPathMatcher("glob:" + var3);
                  File[] var8 = var6.toFile().listFiles();
                  return var8 == null ? var2 : (List)Arrays.stream(var8).filter((var1x) -> var1x.isFile() && var7.matches(var1x.toPath())).map((var0) -> var0.getAbsolutePath()).collect(Collectors.toList());
               }
            }
         }
      }
   }

   private List a(String var1) {
      ArrayList var2 = new ArrayList();
      Map var3 = DVTSymbolCollector.INSTANCE.collectSymbolsPrerequisites(var1, AIUtils.a().b(), true);

      for(Map.Entry var4 : var3.entrySet()) {
         DVTSymbolKind var6 = (DVTSymbolKind)var4.getKey();
         List var7 = (List)var4.getValue();
         if (var6 == DVTSymbolKind.COMPILED_FILE && var7 != null && !var7.isEmpty()) {
            for(ISymbolCollectorPrerequisite var8 : var7) {
               if (var8 instanceof IDVTElementWrapper) {
                  IDVTElementWrapper var10 = (IDVTElementWrapper)var8;
                  LanguageKind var11 = var10.getLanguageKind();
                  IAIContributor var12 = AIContributorManager.INSTANCE.getContributor(var11);
                  if (var12 != null) {
                     ParserPath var13 = var12.c(var10);
                     if (var13 != null && var13.path != null) {
                        var2.add(var13.path);
                     }
                  }
               }
            }
         }
      }

      return var2;
   }

   private boolean a(IDVTElementWrapper var1) {
      LanguageKind var2 = var1.getLanguageKind();
      IAIContributor var3 = AIContributorManager.INSTANCE.getContributor(var2);
      if (var3 == null) {
         return false;
      } else if (var3.b(var1)) {
         ParserPath var5 = var3.c(var1);
         return var5 != null && var5.path != null;
      } else {
         Object var4 = var1.getRfElement();
         if (var4 instanceof IRfNamedElement) {
            return AIUtils.a().b((IRfNamedElement)((IRfNamedElement)var4)) != null;
         } else {
            return false;
         }
      }
   }

   private String a(Object var1) {
      if (var1 instanceof IRfNamedElement && var1 instanceof IMacroInfo) {
         ParserPath var4 = ((IRfNamedElement)var1).getMacroParserPath();
         return var4 == null ? null : var4.path;
      } else if (!(var1 instanceof IDVTElementWrapper)) {
         return null;
      } else {
         Object var2 = ((IDVTElementWrapper)var1).getRfElement();
         if (var2 instanceof IDVTFileInstance) {
            ParserPath var3 = ((IDVTFileInstance)var2).getParserPath();
            return var3 == null ? null : var3.path;
         } else {
            return var2 instanceof IRfNamedElement ? AIUtils.a().b((IRfNamedElement)((IRfNamedElement)var2)) : null;
         }
      }
   }

   private void a(List var1, List var2, int var3, boolean var4) {
      for(ISymbolCollectorPrerequisite var5 : var1) {
         if (var5 instanceof MacrosByCategoryWrapper) {
            LinkedHashMap var7 = ((MacrosByCategoryWrapper)var5).a();

            for(Map.Entry var8 : var7.entrySet()) {
               String var10 = ((IRfNamedElement)var8.getKey()).getSignature();
               if (!"If(n)def Controls".equals(var10) && !"Ifndef Guards".equals(var10)) {
                  List var11 = (List)var8.getValue();
                  if (var11 != null && !var11.isEmpty()) {
                     if (var4) {
                        String var15 = this.a(var11.get(0));
                        int var16 = AIProtectManager.INSTANCE.getProtectLineNumber(var15);
                        if (var16 > 0) {
                           AIUtils.a().b(var15, var16);
                        }

                        var2.add(AIExpansionUtils.INSTANCE.expandNamedElementOrMacro(var11.get(0)));
                        return;
                     }

                     for(Object var12 : var11) {
                        String var14 = this.a(var12);
                        if (!AIProtectManager.INSTANCE.isFileProtected(var14)) {
                           var2.add(AIExpansionUtils.INSTANCE.expandNamedElementOrMacro(var12));
                           if (var2.size() >= var3) {
                              return;
                           }
                        }
                     }
                  }
               }
            }
         }
      }

   }

   private void b(List var1, List var2, int var3, boolean var4) {
      for(ISymbolCollectorPrerequisite var5 : var1) {
         if (var5 instanceof IDVTElementWrapper) {
            String var7 = this.a((Object)var5);
            int var8 = AIProtectManager.INSTANCE.getProtectLineNumber(var7);
            if (var8 > 0) {
               if (var4) {
                  AIUtils.a().b(var7, var8);
               }
            } else {
               var2.add(AIExpansionUtils.INSTANCE.expandNamedElementOrMacro(((IDVTElementWrapper)var5).getRfElement(IRfNamedElement.class)));
               if (var2.size() >= var3) {
                  return;
               }
            }
         }
      }

   }

   private boolean b(String var1) {
      if (var1 != null && !var1.isEmpty()) {
         for(String var2 : g) {
            if (var1.startsWith(var2 + ":")) {
               return true;
            }
         }

         return false;
      } else {
         return false;
      }
   }
}
