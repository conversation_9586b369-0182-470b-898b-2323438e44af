package ro.amiq.dvt.ai;

import java.util.List;
import org.eclipse.core.resources.IProject;
import org.eclipse.jface.text.BadLocationException;
import org.eclipse.jface.text.IDocument;
import org.eclipse.jface.text.IRegion;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.contributor.AIContributorManager;
import ro.amiq.dvt.ai.contributor.IAIContributor;
import ro.amiq.dvt.ai.model.SnippetSelectionType;
import ro.amiq.dvt.model.reflection.IMacroInfo;
import ro.amiq.dvt.model.reflection.IMacroText;
import ro.amiq.dvt.model.reflection.IRfAssociatedTypeElement;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfDesignElement;
import ro.amiq.dvt.model.reflection.IRfFileDef;
import ro.amiq.dvt.model.reflection.IRfNamedElement;
import ro.amiq.dvt.model.reflection.IRfNamedElementAndScope;
import ro.amiq.dvt.model.reflection.IRfPortElement;
import ro.amiq.dvt.model.reflection.IRfScopeElement;
import ro.amiq.dvt.model.reflection.RfMixedLangManager;
import ro.amiq.dvt.model.reflection.StringReplace;
import ro.amiq.dvt.startup.core.DVTLogger;
import ro.amiq.dvt.test.TestHelper;
import ro.amiq.dvt.ui.Utils;
import ro.amiq.dvt.ui.editor.DVTEditor;
import ro.amiq.dvt.ui.editor.DVTSourceViewerConfiguration;
import ro.amiq.dvt.ui.editor.TextUtils;
import ro.amiq.dvt.utils.DVTStringUtil;
import ro.amiq.dvt.utils.DVTUtilsCommon;

public enum AIExpansionUtils {
   INSTANCE;

   private static final String a = "typedef";
   public static final String TICK_DEFINE = "`define";

   public String expandDefElement(IRfDefElement var1, DVTEditor var2, boolean var3) {
      if (var1 == null) {
         return "";
      } else {
         IRfFileDef var4 = var1.getDefFile();
         if (var4 != null && var4.getParserPath() != null && var4.getParserPath().path != null) {
            IDocument var5 = AIUtils.a().a(var4);
            if (var5 == null) {
               return "";
            } else {
               try {
                  IRegion var6 = DVTUtilsCommon.INSTANCE.getFoldingRegion(var1, var5, true);
                  if (var6 == null) {
                     return "";
                  } else {
                     String var7 = var5.get(var6.getOffset(), var6.getLength());
                     if (var7 == null) {
                        return "";
                     } else {
                        if (var3) {
                           String var8 = var2 != null ? this.indentCodeSnippet(var7, var2) : this.indentCodeSnippet(var7, var4.getParserPath().path);
                           if (var8 != null) {
                              var7 = var8;
                           }
                        }

                        String var10 = this.a(var1, var2, var3);
                        return var7 + var10;
                     }
                  }
               } catch (BadLocationException var9) {
                  DVTLogger.INSTANCE.logError((Throwable)var9);
                  return "";
               }
            }
         } else {
            return "";
         }
      }
   }

   public String expandDefElementBasedOnOffsets(IRfDefElement var1) {
      if (var1 == null) {
         return "";
      } else {
         IRfFileDef var2 = var1.getDefFile();
         if (var2 != null && var2.getParserPath() != null && var2.getParserPath().path != null) {
            IDocument var3 = AIUtils.a().a(var2);
            if (var3 == null) {
               return "";
            } else {
               try {
                  return var3.get(var1.getStartOffset(), var1.getEndOffset() - var1.getStartOffset());
               } catch (BadLocationException var5) {
                  DVTLogger.INSTANCE.logError((Throwable)var5);
                  return "";
               }
            }
         } else {
            return "";
         }
      }
   }

   public String expandNamedElementOrMacro(Object var1) {
      if (var1 == null) {
         return "";
      } else if (var1 instanceof IMacroInfo) {
         return this.expandMacro((IMacroInfo)var1);
      } else {
         return var1 instanceof IRfNamedElement ? this.a((IRfNamedElement)var1) : "";
      }
   }

   private String a(IRfNamedElement var1) {
      LanguageKind var2 = var1.getLanguageKind();
      IAIContributor var3 = AIContributorManager.INSTANCE.getContributor(var2);
      return var3 == null ? "" : var3.a(var1);
   }

   public String expandMacro(IMacroInfo var1) {
      if (var1 == null) {
         return "";
      } else {
         StringBuilder var2 = new StringBuilder("`define");
         String var3 = var1.getSignature(false, false);
         if (var3 == null) {
            return "";
         } else {
            var2.append(" ").append(var3).append(" ");
            if (!var1.hasReplacement()) {
               return var2.toString().trim();
            } else {
               IMacroText var4 = var1.getMacroText();
               if (var4 == null) {
                  return var2.toString().trim();
               } else {
                  List var5 = var4.getReplacement();
                  if (var5 != null && !var5.isEmpty()) {
                     for(StringReplace var6 : var5) {
                        String var8 = var6.getString();
                        if (var8 != null) {
                           var2.append(var8);
                        }
                     }

                     return var2.toString().trim();
                  } else {
                     return var2.toString().trim();
                  }
               }
            }
         }
      }
   }

   public String getNamedElementExpandedForSelectionType(IRfNamedElementAndScope var1, SnippetSelectionType var2, DVTEditor var3) {
      LanguageKind var4 = var1.getLanguageKind();
      IAIContributor var5 = AIContributorManager.INSTANCE.getContributor(var4);
      if (var5 == null) {
         return null;
      } else {
         Object var6 = null;
         if (SnippetSelectionType.PORT_LIST.equals(var2)) {
            return this.a(var1);
         } else {
            IRfDefElement var7 = AIUtils.a().a(var1, var5.a(var2), SnippetSelectionType.ELEMENT.equals(var2));
            return this.getImplementationOrDeclarationExpandedForElement(var7, var3);
         }
      }
   }

   public String expandArrayTypedef(IRfAssociatedTypeElement var1) {
      if (!(var1 instanceof IRfNamedElement)) {
         return "";
      } else {
         StringBuilder var2 = new StringBuilder("typedef");
         IRfNamedElement var3 = var1.getAssociatedType();
         var2.append(" ").append(var3.getName()).append(" ");
         var2.append(((IRfNamedElement)var1).getName()).append(";\n");
         return var2.toString();
      }
   }

   private String a(IRfNamedElementAndScope var1) {
      IRfScopeElement var2 = var1.getScope();

      IRfDesignElement var3;
      for(var3 = null; var2 != null; var2 = var2.getEnclosingScope()) {
         if (var2 instanceof IRfDesignElement) {
            var3 = (IRfDesignElement)var2;
            break;
         }

         if (var2 instanceof IRfDefElement) {
            IRfNamedElement var4 = ((IRfDefElement)var2).getNamedElement();
            if (var4 instanceof IRfDesignElement) {
               var3 = (IRfDesignElement)var4;
               break;
            }
         }
      }

      if (var3 == null) {
         return "";
      } else {
         List var8 = var3.getLocalPorts();
         if (var8 != null && !var8.isEmpty()) {
            StringBuilder var5 = new StringBuilder();

            for(IRfPortElement var6 : var8) {
               var5.append(var6.getContextInformationSignature()).append(";\n");
            }

            return var5.toString();
         } else {
            return "";
         }
      }
   }

   public String getImplementationOrDeclarationExpandedForElement(IRfDefElement var1, DVTEditor var2) {
      if (var1 == null) {
         return null;
      } else {
         IRfNamedElement var3 = var1.getNamedElement();
         if (var3 instanceof IRfPortElement) {
            return var3.getSignature();
         } else {
            if (var3 != null) {
               Object var4 = var3.getImplementation();
               if (var4 instanceof IRfDefElement) {
                  return this.expandDefElement((IRfDefElement)var4, var2, true);
               }
            }

            return this.expandDefElement(var1, var2, true);
         }
      }
   }

   private String a(IRfDefElement var1, DVTEditor var2, boolean var3) {
      IRfNamedElement var4 = var1.getNamedElement();
      if (var4 == null) {
         return "";
      } else {
         LanguageKind var5 = var4.getLanguageKind();
         IAIContributor var6 = AIContributorManager.INSTANCE.getContributor(var5);
         return var6 == null ? "" : var6.a(var1, var2, var3);
      }
   }

   public String indentCodeSnippet(String var1, DVTEditor var2) {
      if (var1 != null && var2 != null) {
         DVTSourceViewerConfiguration var3 = var2.e();
         return var3 == null ? var1 : this.a(var1, var3, var2.p());
      } else {
         return var1;
      }
   }

   public String indentCodeSnippet(String var1, String var2) {
      if (var1 != null && var2 != null) {
         int var3 = AIProtectManager.INSTANCE.getProtectLineNumber(var2);
         if (var3 > 0) {
            AIUtils.a().b(var2, var3);
         }

         IProject var4 = AIUtils.a().d();
         if (var4 == null) {
            return var1;
         } else {
            String var5 = AIUtils.a().c(var2);
            if (var5 == null) {
               return var1;
            } else {
               DVTSourceViewerConfiguration var6 = RfMixedLangManager.getInstance().getSourceViewerConfigurationForFile(var5, var4);
               return var6 == null ? var1 : this.a(var1, var6, AIUtils.a().b(var2));
            }
         }
      } else {
         return var1;
      }
   }

   private String a(String var1, DVTSourceViewerConfiguration var2, int var3) {
      if (TestHelper.w()) {
         var3 = AIUtils.a().e();
      }

      var2.b(var3);
      var1 = var1.replace("\r", "");
      String var4 = Utils.b(() -> var2, DVTStringUtil.d(var1, Integer.MAX_VALUE), "", false);
      String var5 = var4 != null ? var4 : var1;
      return TextUtils.a(var5, var3);
   }
}
