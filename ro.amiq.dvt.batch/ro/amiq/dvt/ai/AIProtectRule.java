package ro.amiq.dvt.ai;

import java.nio.file.PathMatcher;
import java.nio.file.Paths;

public class AIProtectRule {
   private boolean a;
   private PathMatcher b;
   private int c;

   public AIProtectRule(boolean var1, PathMatcher var2, int var3) {
      this.a = var1;
      this.b = var2;
      this.c = var3;
   }

   public boolean a() {
      return this.a;
   }

   public boolean a(String var1) {
      return this.b.matches(Paths.get(var1));
   }

   public int b() {
      return this.c;
   }
}
