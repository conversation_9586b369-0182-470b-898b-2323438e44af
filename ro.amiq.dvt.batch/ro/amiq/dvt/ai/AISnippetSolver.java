package ro.amiq.dvt.ai;

import java.util.function.BooleanSupplier;
import org.eclipse.jface.text.IDocument;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.model.EditorPosition;
import ro.amiq.dvt.ai.model.SnippetSelectionType;
import ro.amiq.dvt.ls.LSDocument;
import ro.amiq.dvt.ls.LSEditorsManager;
import ro.amiq.dvt.ls.documentSymbols.VhdlDocumentSymbolsUtil;
import ro.amiq.dvt.ls.documentSymbols.VlogDocumentSymbolsUtil;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.ui.editor.DVTEditor;
import ro.amiq.dvt.ui.views.IDVTElementWrapper;

public class AISnippetSolver extends AISnippetSolverCommon {
   private static final Object b = new Object();
   private static AISnippetSolver c;
   // $FF: synthetic field
   private static int[] d;

   public static AISnippetSolver a() {
      if (c == null) {
         synchronized(b) {
            c = new AISnippetSolver();
         }
      }

      return c;
   }

   protected IDVTElementWrapper a(String var1) {
      DVTEditor var2 = LSEditorsManager.INSTANCE.getOrInitializeEditor(var1);
      if (var2 == null) {
         return null;
      } else {
         IDocument var3 = var2.n();
         if (!(var3 instanceof LSDocument)) {
            return null;
         } else {
            IDVTElementWrapper var4 = null;
            LanguageKind var5 = var2.l();
            if (LanguageKind.VLOG.equals(var5)) {
               var4 = VlogDocumentSymbolsUtil.c().a(var2, true);
            } else if (LanguageKind.VHDL.equals(var5)) {
               var4 = VhdlDocumentSymbolsUtil.c().a(var2, true);
            }

            return var4;
         }
      }
   }

   protected IDVTElementWrapper a(IRfDefElement var1) {
      if (var1 == null) {
         return null;
      } else {
         DVTEditor var2 = AIUtils.a().a(var1);
         if (var2 == null) {
            return null;
         } else if (LanguageKind.VLOG.equals(var2.l())) {
            return VlogDocumentSymbolsUtil.c().a(var1, var2, true);
         } else {
            return LanguageKind.VHDL.equals(var2.l()) ? VhdlDocumentSymbolsUtil.c().a(var1, var2, true) : null;
         }
      }
   }

   protected String b() {
      return LSEditorsManager.INSTANCE.getActiveEditorNormalizedPath();
   }

   public String a(EditorPosition var1, SnippetSelectionType var2, String var3, BooleanSupplier var4) {
      String var5 = var1.c();
      int var6 = AIProtectManager.INSTANCE.getProtectLineNumber(var5);
      if (var6 > 0) {
         AIUtils.a().b(var5, var6);
         return null;
      } else {
         switch (c()[var2.ordinal()]) {
            case 1:
            case 2:
               return AIExpansionUtils.INSTANCE.indentCodeSnippet(var3, var5);
            default:
               DVTEditor var7 = LSEditorsManager.INSTANCE.getOrInitializeEditor(var5);
               return var7 == null ? null : this.a(var1, var2, var7, var4);
         }
      }
   }

   // $FF: synthetic method
   static int[] c() {
      int[] var10000 = d;
      if (var10000 != null) {
         return var10000;
      } else {
         int[] var0 = new int[SnippetSelectionType.values().length];

         try {
            var0[SnippetSelectionType.ACTION_BLOCK.ordinal()] = 5;
         } catch (NoSuchFieldError var14) {
         }

         try {
            var0[SnippetSelectionType.ARCHITECTURE.ordinal()] = 13;
         } catch (NoSuchFieldError var13) {
         }

         try {
            var0[SnippetSelectionType.CLASS.ordinal()] = 8;
         } catch (NoSuchFieldError var12) {
         }

         try {
            var0[SnippetSelectionType.CODE.ordinal()] = 1;
         } catch (NoSuchFieldError var11) {
         }

         try {
            var0[SnippetSelectionType.CONFIGURATION.ordinal()] = 14;
         } catch (NoSuchFieldError var10) {
         }

         try {
            var0[SnippetSelectionType.CONTAINER.ordinal()] = 3;
         } catch (NoSuchFieldError var9) {
         }

         try {
            var0[SnippetSelectionType.ELEMENT.ordinal()] = 4;
         } catch (NoSuchFieldError var8) {
         }

         try {
            var0[SnippetSelectionType.ENTITY.ordinal()] = 12;
         } catch (NoSuchFieldError var7) {
         }

         try {
            var0[SnippetSelectionType.FILE.ordinal()] = 2;
         } catch (NoSuchFieldError var6) {
         }

         try {
            var0[SnippetSelectionType.INTERFACE.ordinal()] = 10;
         } catch (NoSuchFieldError var5) {
         }

         try {
            var0[SnippetSelectionType.METHOD.ordinal()] = 6;
         } catch (NoSuchFieldError var4) {
         }

         try {
            var0[SnippetSelectionType.MODULE.ordinal()] = 9;
         } catch (NoSuchFieldError var3) {
         }

         try {
            var0[SnippetSelectionType.PACKAGE.ordinal()] = 11;
         } catch (NoSuchFieldError var2) {
         }

         try {
            var0[SnippetSelectionType.PORT_LIST.ordinal()] = 7;
         } catch (NoSuchFieldError var1) {
         }

         d = var0;
         return var0;
      }
   }
}
