package ro.amiq.dvt.ai.model;

import ro.amiq.dvt.ai.model.adapters.IStringRepresentableEnum;

public enum SnippetExamplesOfType implements IStringRepresentableEnum {
   UVM_AGENT("uvm_agent"),
   UVM_DRIVER("uvm_driver"),
   UVM_ENV("uvm_env"),
   UVM_MONITOR("uvm_monitor"),
   UVM_SEQUENCE_ITEM("uvm_sequence_item"),
   UVM_REG("uvm_reg"),
   UVM_REG_BACKDOOR("uvm_reg_backdoor"),
   UVM_REG_FRONTDOOR("uvm_reg_frontdoor"),
   UVM_REG_ADAPTER("uvm_reg_adapter"),
   UVM_REG_BLOCK("uvm_reg_block"),
   UVM_REG_FIELD("uvm_reg_field"),
   UVM_REG_MAP("uvm_reg_map"),
   UVM_REG_PREDICTOR("uvm_reg_predictor"),
   UVM_REG_SEQUENCE("uvm_reg_sequence"),
   UVM_REG_FIFO("uvm_reg_fifo"),
   UVM_REG_FILE("uvm_reg_file"),
   UVM_SCOREBOARD("uvm_scoreboard"),
   UVM_SEQUENCER("uvm_sequencer"),
   UVM_SEQUENCE("uvm_sequence"),
   UVM_TEST("uvm_test"),
   UVM_COMPONENT("uvm_component"),
   UVM_OBJECT("uvm_object"),
   UVM_MEM("uvm_mem");

   private String type;

   private SnippetExamplesOfType(String var3) {
      this.type = var3;
   }

   public String getValue() {
      return this.type;
   }
}
