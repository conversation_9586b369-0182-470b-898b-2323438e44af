package ro.amiq.dvt.ai.model;

import com.google.gson.annotations.SerializedName;
import java.util.Objects;
import ro.amiq.dvt.ai.AIUtils;

public class Dependency {
   @SerializedName("isOutline")
   private boolean a;
   @SerializedName("outline")
   private MultiRootTree b;
   @SerializedName("codeSnippet")
   private CodeAndLineRange c;

   public Dependency(boolean var1, MultiRootTree var2, CodeAndLineRange var3) {
      this.a = var1;
      this.b = var2;
      this.c = var3;
   }

   public boolean a() {
      return this.a;
   }

   public void a(boolean var1) {
      this.a = var1;
   }

   public MultiRootTree b() {
      return this.b;
   }

   public void a(MultiRootTree var1) {
      this.b = var1;
   }

   public CodeAndLineRange c() {
      return this.c;
   }

   public void a(CodeAndLineRange var1) {
      this.c = var1;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.c, this.a, this.b});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         Dependency var2 = (Dependency)var1;
         return Objects.equals(this.c, var2.c) && this.a == var2.a && Objects.equals(this.b, var2.b);
      }
   }

   public int d() {
      return this.a ? AIUtils.a().a((TreeElement)((TreeElement)this.b.a().get(0))) : this.c.a().length();
   }
}
