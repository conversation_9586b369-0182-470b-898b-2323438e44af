package ro.amiq.dvt.ai.model;

import ro.amiq.dvt.ai.model.adapters.IStringRepresentableEnum;

public enum SnippetSelectionType implements IStringRepresentableEnum {
   CODE("code"),
   FILE("file"),
   CONTAINER("container"),
   ELEMENT("element"),
   ACTION_BLOCK("action_block"),
   METHOD("method"),
   PORT_LIST("port_list"),
   CLASS("class"),
   MODULE("module"),
   INTERFACE("interface"),
   PACKAGE("package"),
   ENTITY("entity"),
   ARCHITECTURE("architecture"),
   CONFIGURATION("configuration");

   private String text;

   private SnippetSelectionType(String var3) {
      this.text = var3;
   }

   public String getValue() {
      return this.text;
   }
}
