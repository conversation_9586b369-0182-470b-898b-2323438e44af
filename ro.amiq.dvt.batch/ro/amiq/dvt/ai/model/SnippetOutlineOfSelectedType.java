package ro.amiq.dvt.ai.model;

import ro.amiq.dvt.ai.model.adapters.IStringRepresentableEnum;

public enum SnippetOutlineOfSelectedType implements IStringRepresentableEnum {
   FILE("file"),
   ELEMENT("element"),
   CONTAINER("container");

   private String type;

   private SnippetOutlineOfSelectedType(String var3) {
      this.type = var3;
   }

   public String getValue() {
      return this.type;
   }
}
