package ro.amiq.dvt.ai.model;

import ro.amiq.dvt.ai.model.adapters.IStringRepresentableEnum;

public enum DependenciesExpansionStrategy implements IStringRepresentableEnum {
   FORCE_OUTLINE("force_outline"),
   FORCE_CODE_SNIPPET("force_code_snippet"),
   ADAPTIVE("adaptive");

   private String strategy;

   private DependenciesExpansionStrategy(String var3) {
      this.strategy = var3;
   }

   public String getValue() {
      return this.strategy;
   }
}
