package ro.amiq.dvt.ai.model;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import java.util.Objects;

public class MultiRootTree {
   @SerializedName("roots")
   private List a;

   public MultiRootTree() {
   }

   public MultiRootTree(List var1) {
      this.a = var1;
   }

   public List a() {
      return this.a;
   }

   public void a(List var1) {
      this.a = var1;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.a});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         MultiRootTree var2 = (MultiRootTree)var1;
         return Objects.equals(this.a, var2.a);
      }
   }
}
