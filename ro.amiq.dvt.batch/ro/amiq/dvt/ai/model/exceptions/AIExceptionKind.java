package ro.amiq.dvt.ai.model.exceptions;

public enum AIExceptionKind {
   PROTECTED_FILE_ACCESS(0, "File '%s' is protected and can not be accessed by AI Assistant!"),
   UNEXPECTED_BINARY_FILE(1, "File '%s' is binary and can not be included in the prompt!"),
   DH_NOT_AVAILABLE(2, "Design hierarchy is not available!"),
   VH_NOT_AVAILABLE(3, "Verification hierarchy is not available!"),
   NO_EDITOR_FOUND(4, "No editor found!"),
   NO_ELEMENT_SELECTED(5, "No element is selected!"),
   SYMBOL_EXPANSION_FAILURE(6, "Failed to expand symbol '#%s'. Expanding it to an empty value instead."),
   SNIPPET_EXPANSION_FAILURE(7, "Failed to expand snippet '@%s'. Expanding it to an empty value instead."),
   REQUEST_CANCELED(8, "%s request was canceled!"),
   INTERNAL_ERROR(9, "Internal error!"),
   CLIPBOARD_NOT_ENABLED(10, "Failed to expand snippet '@%s'. Reason: Disabled. Please enable it from the preference page.");

   public final int KIND;
   public final String MESSAGE;

   private AIExceptionKind(int var3, String var4) {
      this.KIND = var3;
      this.MESSAGE = var4;
   }
}
