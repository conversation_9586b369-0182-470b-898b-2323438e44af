package ro.amiq.dvt.ai.model.exceptions;

import com.google.gson.annotations.SerializedName;
import java.util.Objects;

public class AIProtectExceptionData extends AIExceptionData {
   @SerializedName("protectFileLine")
   private int a;

   public AIProtectExceptionData(int var1, String var2, int var3, int var4) {
      super(var1, var2, var3);
      this.a = var4;
   }

   public String b() {
      String var1 = this.a();
      if (var1 == null) {
         return null;
      } else {
         String[] var2 = var1.split("'");
         return var2.length < 3 ? null : var2[1];
      }
   }

   public int c() {
      return this.a;
   }

   public int hashCode() {
      int var1 = super.hashCode();
      var1 = 31 * var1 + Objects.hash(new Object[]{this.a});
      return var1;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!super.equals(var1)) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         AIProtectExceptionData var2 = (AIProtectExceptionData)var1;
         return this.a == var2.a;
      }
   }
}
