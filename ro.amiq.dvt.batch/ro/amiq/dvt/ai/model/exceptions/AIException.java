package ro.amiq.dvt.ai.model.exceptions;

import org.eclipse.lsp4j.jsonrpc.ResponseErrorException;
import org.eclipse.lsp4j.jsonrpc.messages.ResponseError;
import org.eclipse.lsp4j.jsonrpc.messages.ResponseErrorCode;
import ro.amiq.dvt.logging.UnloggableException;

public abstract class AIException extends ResponseErrorException implements UnloggableException {
   private static final long serialVersionUID = 1L;
   public static final int SEVERITY_ERROR = 0;
   public static final int SEVERITY_WARNING = 1;
   public static final int SEVERITY_INFO = 2;

   protected AIException(AIExceptionData var1) {
      super(new ResponseError(ResponseErrorCode.RequestFailed, var1.a(), var1));
   }
}
