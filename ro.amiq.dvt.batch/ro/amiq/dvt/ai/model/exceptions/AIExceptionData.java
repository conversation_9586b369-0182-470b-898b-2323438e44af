package ro.amiq.dvt.ai.model.exceptions;

import com.google.gson.annotations.SerializedName;
import java.util.Objects;

public class AIExceptionData {
   @SerializedName("exceptionKind")
   private int a;
   @SerializedName("message")
   private String b;
   @SerializedName("severity")
   private int c;

   public AIExceptionData(int var1, String var2, int var3) {
      this.a = var1;
      this.b = var2;
      this.c = var3;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.a, this.b, this.c});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         AIExceptionData var2 = (AIExceptionData)var1;
         return this.a == var2.a && Objects.equals(this.b, var2.b) && this.c == var2.c;
      }
   }

   public String a() {
      return this.b;
   }
}
