package ro.amiq.dvt.ai.model;

import ro.amiq.dvt.ai.model.adapters.IStringRepresentableEnum;

public enum LogSeverity implements IStringRepresentableEnum {
   TRACE("Trace"),
   DEBUG("Debug"),
   INFO("Info"),
   WARNING("Warning"),
   ERROR("Error");

   String severity;

   private LogSeverity(String var3) {
      this.severity = var3;
   }

   public String getValue() {
      return this.severity;
   }
}
