package ro.amiq.dvt.ai.model;

import com.google.gson.annotations.SerializedName;
import java.util.Objects;

public class CodeAndLineRange {
   @SerializedName("code")
   private String a;
   @SerializedName("filePath")
   private String b;
   @SerializedName("startLine")
   private int c;
   @SerializedName("endLine")
   private int d;

   public CodeAndLineRange(String var1, String var2, int var3, int var4) {
      this.a = var1;
      this.b = var2;
      this.c = var3;
      this.d = var4;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.a, this.d, this.b, this.c});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         CodeAndLineRange var2 = (CodeAndLineRange)var1;
         return Objects.equals(this.a, var2.a) && this.d == var2.d && Objects.equals(this.b, var2.b) && this.c == var2.c;
      }
   }

   public String a() {
      return this.a;
   }

   public void a(String var1) {
      this.a = var1;
   }

   public String b() {
      return this.b;
   }

   public void b(String var1) {
      this.b = var1;
   }

   public int c() {
      return this.c;
   }

   public void a(int var1) {
      this.c = var1;
   }

   public int d() {
      return this.d;
   }

   public void b(int var1) {
      this.d = var1;
   }
}
