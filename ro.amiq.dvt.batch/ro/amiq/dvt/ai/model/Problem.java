package ro.amiq.dvt.ai.model;

import com.google.gson.annotations.SerializedName;
import java.util.Objects;

public class Problem {
   @SerializedName("filePath")
   private String a;
   @SerializedName("line")
   private int b;
   @SerializedName("message")
   private String c;
   @SerializedName("severity")
   private String d;
   @SerializedName("surroundingCode")
   private String e;

   public Problem(String var1, int var2, String var3, String var4, String var5) {
      this.a = var1;
      this.b = var2;
      this.c = var3;
      this.d = var4;
      this.e = var5;
   }

   public String a() {
      return this.a;
   }

   public void a(String var1) {
      this.a = var1;
   }

   public int b() {
      return this.b;
   }

   public void a(int var1) {
      this.b = var1;
   }

   public String c() {
      return this.c;
   }

   public void b(String var1) {
      this.c = var1;
   }

   public String d() {
      return this.d;
   }

   public void c(String var1) {
      this.d = var1;
   }

   public String e() {
      return this.e;
   }

   public void d(String var1) {
      this.e = var1;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.a, this.b, this.c, this.d, this.e});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         Problem var2 = (Problem)var1;
         return Objects.equals(this.a, var2.a) && this.b == var2.b && Objects.equals(this.c, var2.c) && Objects.equals(this.d, var2.d) && Objects.equals(this.e, var2.e);
      }
   }
}
