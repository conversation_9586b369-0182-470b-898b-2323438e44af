package ro.amiq.dvt.ai.model;

import com.google.gson.annotations.SerializedName;
import java.util.Objects;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.runtime.IPath;

public class EditorRange {
   @SerializedName("file")
   private IFile a;
   @SerializedName("startOffset")
   private int b;
   @SerializedName("endOffset")
   private int c;

   public EditorRange(IFile var1, int var2, int var3) {
      this.a = var1;
      this.b = var2;
      this.c = var3;
   }

   public IFile a() {
      return this.a;
   }

   public String b() {
      if (this.a == null) {
         return null;
      } else {
         IPath var1 = this.a.getLocation();
         return var1 == null ? null : var1.toOSString();
      }
   }

   public int c() {
      return this.b;
   }

   public int d() {
      return this.c;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.c, this.b(), this.b});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         EditorRange var2 = (EditorRange)var1;
         return this.c == var2.c && Objects.equals(this.b(), var2.b()) && this.b == var2.b;
      }
   }
}
