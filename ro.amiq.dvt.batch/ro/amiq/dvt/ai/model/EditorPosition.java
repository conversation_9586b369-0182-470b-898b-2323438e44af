package ro.amiq.dvt.ai.model;

import com.google.gson.annotations.SerializedName;
import org.eclipse.core.resources.IFile;
import org.eclipse.core.runtime.IPath;

public class EditorPosition {
   @SerializedName("file")
   private IFile a;
   @SerializedName("offset")
   private int b;

   public EditorPosition(IFile var1, int var2) {
      this.a = var1;
      this.b = var2;
   }

   public IFile a() {
      return this.a;
   }

   public int b() {
      return this.b;
   }

   public String c() {
      if (this.a == null) {
         return null;
      } else {
         IPath var1 = this.a.getLocation();
         return var1 == null ? null : var1.toOSString();
      }
   }
}
