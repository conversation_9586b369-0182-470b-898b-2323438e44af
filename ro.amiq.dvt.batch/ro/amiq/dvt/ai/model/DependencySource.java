package ro.amiq.dvt.ai.model;

import java.util.Objects;
import ro.amiq.dvt.model.reflection.IRfDefElement;

public class DependencySource {
   private boolean a;
   private boolean b;
   private String c;
   private String d;
   private IRfDefElement e;
   private int f;

   public DependencySource(boolean var1, boolean var2, String var3, String var4, IRfDefElement var5, int var6) {
      this.a = var1;
      this.b = var2;
      this.c = var3;
      this.d = var4;
      this.e = var5;
      this.f = var6;
   }

   public boolean a() {
      return this.a;
   }

   public void a(boolean var1) {
      this.a = var1;
   }

   public boolean b() {
      return this.b;
   }

   public void b(boolean var1) {
      this.b = var1;
   }

   public String c() {
      return this.c;
   }

   public void a(String var1) {
      this.c = var1;
   }

   public String d() {
      return this.d;
   }

   public void b(String var1) {
      this.d = var1;
   }

   public IRfDefElement e() {
      return this.e;
   }

   public void a(IRfDefElement var1) {
      this.e = var1;
   }

   public int f() {
      return this.f;
   }

   public void a(int var1) {
      this.f = var1;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.e, this.a, this.b, this.d, this.c});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         DependencySource var2 = (DependencySource)var1;
         return Objects.equals(this.e, var2.e) && this.a == var2.a && this.b == var2.b && Objects.equals(this.d, var2.d);
      }
   }
}
