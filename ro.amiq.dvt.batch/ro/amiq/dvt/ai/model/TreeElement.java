package ro.amiq.dvt.ai.model;

import com.google.gson.annotations.SerializedName;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class TreeElement {
   @SerializedName("element")
   private String a;
   @SerializedName("children")
   private List b;

   public TreeElement() {
   }

   public TreeElement(String var1, List var2) {
      this.a = var1;
      this.b = var2;
   }

   public void a(String var1) {
      this.a = var1;
   }

   public void a(TreeElement var1) {
      if (this.b == null) {
         this.b = new ArrayList();
      }

      this.b.add(var1);
   }

   public String a() {
      return this.a;
   }

   public List b() {
      return this.b;
   }

   public int hashCode() {
      return Objects.hash(new Object[]{this.b, this.a});
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (var1 == null) {
         return false;
      } else if (this.getClass() != var1.getClass()) {
         return false;
      } else {
         TreeElement var2 = (TreeElement)var1;
         return Objects.equals(this.b, var2.b) && Objects.equals(this.a, var2.a);
      }
   }
}
