package ro.amiq.dvt.ai;

import java.util.function.BooleanSupplier;
import org.eclipse.core.internal.resources.File;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.jface.text.IDocument;
import org.eclipse.lsp4j.Location;
import org.eclipse.lsp4j.MessageType;
import org.eclipse.lsp4j.TextDocumentPositionParams;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.contributor.AIContributorManager;
import ro.amiq.dvt.ai.contributor.IAIContributor;
import ro.amiq.dvt.ai.model.EditorPosition;
import ro.amiq.dvt.ai.model.EditorRange;
import ro.amiq.dvt.ai.model.LogSeverity;
import ro.amiq.dvt.ai.model.LogTarget;
import ro.amiq.dvt.ai.model.SnippetSelectionType;
import ro.amiq.dvt.ai.model.exceptions.AIExceptionData;
import ro.amiq.dvt.ai.model.exceptions.AIExceptionKind;
import ro.amiq.dvt.ai.model.exceptions.AIRequestCancellationException;
import ro.amiq.dvt.ls.LClient;
import ro.amiq.dvt.ls.LSDocumentManager;
import ro.amiq.dvt.ls.LSEditorsManager;
import ro.amiq.dvt.ls.LSManager;
import ro.amiq.dvt.ls.LSUtils;
import ro.amiq.dvt.ls.LanguageKindDispatcher;
import ro.amiq.dvt.ls.ai.extendedDataTypes.GetContainerRangeResponse;
import ro.amiq.dvt.ls.ai.extendedDataTypes.LogParams;
import ro.amiq.dvt.ls.ai.extendedDataTypes.OpenProtectFileAtLineParams;
import ro.amiq.dvt.ls.ai.extendedDataTypes.WriteToAIConsoleParams;
import ro.amiq.dvt.ls.preferences.LSPreferencesManager;
import ro.amiq.dvt.ls.workspaceSymbols.WorkspaceSymbolsManager;
import ro.amiq.dvt.ls.workspaceSymbols.WorkspaceSymbolsView;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfFileDef;
import ro.amiq.dvt.model.reflection.IRfNamedElement;
import ro.amiq.dvt.model.reflection.IRfNamedElementAndScope;
import ro.amiq.dvt.model.reflection.IRfScopeElement;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.ui.editor.DVTEditor;
import ro.amiq.dvt.ui.views.symbolcollector.ISymbolCollectorView;
import ro.amiq.vhdldt.model.reflection.IRfScope;
import ro.amiq.vhdldt.model.reflection.util.RfWNamedElementAndScope;

public class AIUtils extends AIUtilsCommon {
   private static final Object e = new Object();
   private static AIUtils f;

   public static AIUtils a() {
      if (f == null) {
         synchronized(e) {
            f = new AIUtils();
         }
      }

      return f;
   }

   public EditorPosition a(TextDocumentPositionParams var1) {
      if (var1 == null) {
         return null;
      } else {
         String var2 = LSUtils.INSTANCE.convertFileURIToFilePath(var1.getTextDocument().getUri());
         if (var2 == null) {
            return null;
         } else {
            IDocument var3 = LSDocumentManager.INSTANCE.getDocument(var2);
            if (var3 == null) {
               return null;
            } else {
               int var4 = LSUtils.INSTANCE.convertPositionToOffset(var3, var1.getPosition());
               return new EditorPosition(new File(var2, LSManager.INSTANCE.getIProject()), var4);
            }
         }
      }
   }

   public DVTEditor a(IRfDefElement var1) {
      if (var1 == null) {
         return null;
      } else {
         IRfFileDef var2 = var1.getDefFile();
         if (var2 == null) {
            return null;
         } else {
            ParserPath var3 = var2.getParserPath();
            return var3 != null && var3.path != null ? LSEditorsManager.INSTANCE.getOrInitializeEditor(var3.path) : null;
         }
      }
   }

   public EditorRange a(Location var1) {
      String var2 = LSUtils.INSTANCE.convertFileURIToFilePath(var1.getUri());
      if (var2 == null) {
         return null;
      } else {
         IDocument var3 = LSDocumentManager.INSTANCE.getDocument(var2);
         if (var3 == null) {
            return null;
         } else {
            int var4 = LSUtils.INSTANCE.convertPositionToOffset(var3, var1.getRange().getStart());
            int var5 = LSUtils.INSTANCE.convertPositionToOffset(var3, var1.getRange().getEnd());
            return new EditorRange(new File(var2, LSManager.INSTANCE.getIProject()), var4, var5);
         }
      }
   }

   public IDocument a(IRfFileDef var1) {
      IDocument var2 = var1.getDocument();
      if (var2 != null) {
         return var2;
      } else {
         ParserPath var3 = var1.getParserPath();
         if (var3 != null && var3.path != null) {
            DVTEditor var4 = LSEditorsManager.INSTANCE.getOrInitializeEditor(var3.path);
            return var4 == null ? null : var4.n();
         } else {
            return null;
         }
      }
   }

   protected IDocument a(String var1) {
      DVTEditor var2 = LSEditorsManager.INSTANCE.getOrInitializeEditor(var1);
      return var2 == null ? null : var2.n();
   }

   protected IRfNamedElementAndScope a(EditorPosition var1) {
      String var2 = var1.c();
      if (var2 == null) {
         return null;
      } else {
         DVTEditor var3 = LSEditorsManager.INSTANCE.getOrInitializeEditor(var2);
         if (var3 == null) {
            return null;
         } else {
            IDocument var4 = var3.n();
            if (var4 == null) {
               return null;
            } else {
               IRfNamedElementAndScope var5 = LSUtils.INSTANCE.getNamedElementAndScopeAtOffset(var4, var1.b());
               if (var5 != null) {
                  return var5;
               } else {
                  LanguageKind var6 = LanguageKindDispatcher.INSTANCE.getLanguageKindForFile(var1.c());
                  if (var6 != LanguageKind.VHDL) {
                     return null;
                  } else {
                     IRfScopeElement var7 = LSUtils.INSTANCE.getScopeAtOffset(var4, var1.b());
                     return !(var7 instanceof IRfScope) ? null : new RfWNamedElementAndScope((IRfNamedElement)null, (IRfScope)var7);
                  }
               }
            }
         }
      }
   }

   protected void a(LogTarget var1, String var2, LogSeverity var3, String var4) {
      LClient.INSTANCE.run((var4x) -> var4x.writeToAIConsole(new WriteToAIConsoleParams(new LogParams(var1, var3, var2, var4))));
   }

   protected ISymbolCollectorView b() {
      return WorkspaceSymbolsManager.INSTANCE.getView();
   }

   protected void a(int var1) {
      LSPreferencesManager.INSTANCE.updateNumberOfSymbolsPreference(var1);
   }

   protected int c() {
      int var1 = LSPreferencesManager.INSTANCE.getCurrentPreferenceValueAsInt("DVT.workspaceSymbols.maximumNumberOfSymbols");
      LSPreferencesManager.INSTANCE.updateNumberOfSymbolsPreference(1);
      return var1;
   }

   public IProject d() {
      return LSManager.INSTANCE.getIProject();
   }

   protected int b(String var1) {
      DVTEditor var2 = LSEditorsManager.INSTANCE.getEditor(var1);
      return var2 != null && var2.p() > 0 ? var2.p() : this.e();
   }

   protected int e() {
      int var1 = LSPreferencesManager.INSTANCE.getCurrentPreferenceValueAsInt("editor.tabSize");
      return var1 > 0 ? var1 : 4;
   }

   protected String c(String var1) {
      LanguageKind var2 = LanguageKindDispatcher.INSTANCE.getLanguageKindForFile(var1);
      return var2 == null ? null : var2.NATURE_ID;
   }

   public void a(IProgressMonitor var1) {
      ISymbolCollectorView var2 = a().b();
      if (var2 instanceof WorkspaceSymbolsView) {
         WorkspaceSymbolsView var3 = (WorkspaceSymbolsView)var2;
         var3.a(var1);
      }
   }

   public void a(String var1, int var2) {
      this.a(LogTarget.AI_CONSOLE, var1, LogSeverity.WARNING, (String)null);
      LClient.INSTANCE.showPopupWithActionsAsync(MessageType.Warning, var1, "Edit").thenAcceptAsync((var1x) -> {
         if ("Edit".equals(var1x)) {
            LClient.INSTANCE.run((var1) -> var1.openProtectFileAtLine(new OpenProtectFileAtLineParams(var2)));
         }

      });
   }

   public GetContainerRangeResponse b(EditorPosition var1) {
      IRfNamedElementAndScope var2 = this.a(var1);
      if (var2 == null) {
         return null;
      } else {
         LanguageKind var3 = var2.getLanguageKind();
         IAIContributor var4 = AIContributorManager.INSTANCE.getContributor(var3);
         if (var4 == null) {
            return null;
         } else {
            IRfDefElement var5 = a().a(var2, var4.a(SnippetSelectionType.CONTAINER), true);
            if (var5 == null) {
               return null;
            } else {
               IRfFileDef var6 = var5.getDefFile();
               if (var6 == null) {
                  return null;
               } else {
                  ParserPath var7 = var6.getParserPath();
                  return var7 != null && var7.path != null ? new GetContainerRangeResponse(var7.path, var5.getStartLine(), var5.getEndLine()) : null;
               }
            }
         }
      }
   }

   public DVTEditor f() {
      return LSEditorsManager.INSTANCE.getActiveEditor();
   }

   public void a(BooleanSupplier var1, String var2) {
      if (var1 != null) {
         if (var1.getAsBoolean()) {
            String var3 = String.format(AIExceptionKind.REQUEST_CANCELED.MESSAGE, "Solve snippet '@" + var2 + "'");
            throw new AIRequestCancellationException(new AIExceptionData(AIExceptionKind.REQUEST_CANCELED.KIND, var3, 2));
         }
      }
   }

   public void b(BooleanSupplier var1, String var2) {
      if (var1 != null) {
         if (var1.getAsBoolean()) {
            String var3 = String.format(AIExceptionKind.REQUEST_CANCELED.MESSAGE, "Solve symbol '#" + var2 + "'");
            throw new AIRequestCancellationException(new AIExceptionData(AIExceptionKind.REQUEST_CANCELED.KIND, var3, 2));
         }
      }
   }
}
