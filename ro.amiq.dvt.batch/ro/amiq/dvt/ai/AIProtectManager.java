package ro.amiq.dvt.ai;

import java.io.File;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import java.nio.file.PathMatcher;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.eclipse.core.resources.IProject;
import ro.amiq.dvt.buildconfig.BuildConfigManager;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfFileDef;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.utils.DVTFileUtils;
import ro.amiq.dvt.utils.Utils;

public enum AIProtectManager {
   INSTANCE;

   private static final String a = "**";
   private static final String b = "glob:";
   private static final Pattern c = Pattern.compile("\\$\\{?(?<varName>[A-Za-z_][A-Za-z0-9_]*)\\}?");
   private static final String d = "\\";
   private static final String e = "\\\\";
   private static final String f = "#";
   private static final String g = "!";
   private static final long h = 5000L;
   long cacheExpirationTime;
   long protectFileLastModifyTime;
   long protectFileLastSize;
   private List fileProtectRules = new ArrayList();

   public void parseProtectFile(boolean var1) {
      this.fileProtectRules.clear();
      Path var2 = this.b();
      if (var2 != null) {
         if (var1) {
            this.protectFileLastModifyTime = var2.toFile().lastModified();
            this.protectFileLastSize = var2.toFile().length();
         }

         String var3 = Utils.a(var2.toString(), 0, Integer.MAX_VALUE);
         String[] var4 = var3.split(System.lineSeparator());
         ArrayList var5 = new ArrayList();
         int var6 = 0;

         for(String var7 : var4) {
            String var11 = var7.trim();
            ++var6;
            if (!var11.isEmpty() && !var11.startsWith("#")) {
               boolean var12 = var11.startsWith("!");
               String var13 = var12 ? var11.substring("!".length()) : var11;
               PathMatcher var14 = this.a(var13, var5, var6);
               if (var14 != null) {
                  this.fileProtectRules.add(new AIProtectRule(var12, var14, var6));
               }
            }
         }

         if (var1 && !var5.isEmpty()) {
            List var15 = (List)var5.stream().map((var0) -> var0.b).collect(Collectors.toList());
            String var16 = AIUtils.a().a((List)var15);
            AIUtils.a().a(var16, ((InvalidEnvVarInfo)var5.get(0)).a);
         }

      }
   }

   public int getProtectLineNumber(String var1) {
      if (var1 == null) {
         return 0;
      } else {
         Path var2 = Paths.get(var1);
         if (var2 == null) {
            return 0;
         } else {
            var1 = var2.normalize().toString();
            this.a();
            boolean var3 = false;
            int var4 = 0;

            for(AIProtectRule var5 : this.fileProtectRules) {
               if (var5.a(var1)) {
                  var3 = !var5.a();
                  var4 = var5.b();
               }
            }

            return var3 ? var4 : 0;
         }
      }
   }

   public boolean isFileProtected(String var1) {
      return this.getProtectLineNumber(var1) > 0;
   }

   public boolean isDefElementProtected(IRfDefElement var1) {
      if (var1 == null) {
         return false;
      } else {
         IRfFileDef var2 = var1.getDefFile();
         if (var2 == null) {
            return false;
         } else {
            ParserPath var3 = var2.getParserPath();
            return var3 != null && var3.path != null ? this.isFileProtected(var3.path) : false;
         }
      }
   }

   private void a() {
      if (System.currentTimeMillis() >= this.cacheExpirationTime) {
         this.cacheExpirationTime = System.currentTimeMillis() + 5000L;
         Path var1 = this.b();
         if (var1 == null) {
            this.fileProtectRules.clear();
         } else {
            long var2 = var1.toFile().lastModified();
            long var4 = var1.toFile().length();
            if (var2 != this.protectFileLastModifyTime || var4 != this.protectFileLastSize) {
               this.protectFileLastModifyTime = var2;
               this.protectFileLastSize = var4;
               this.parseProtectFile(false);
            }
         }
      }
   }

   private PathMatcher a(String var1, List var2, int var3) {
      var1 = this.b(var1, var2, var3);
      if (var1 == null) {
         return null;
      } else {
         var1 = this.a(var1);
         if (var1 == null) {
            return null;
         } else {
            var1 = var1.replace("**" + File.separator, "**");
            if (DVTFileUtils.a().j(var1)) {
               var1 = var1.replace("\\", "\\\\");
            }

            return FileSystems.getDefault().getPathMatcher("glob:" + var1);
         }
      }
   }

   private String b(String var1, List var2, int var3) {
      IProject var4 = AIUtils.a().d();
      if (var4 == null) {
         return null;
      } else {
         Map var5 = BuildConfigManager.q(var4);
         Matcher var6 = c.matcher(var1);

         StringBuffer var7;
         String var9;
         for(var7 = new StringBuffer(); var6.find(); var6.appendReplacement(var7, Matcher.quoteReplacement(var9))) {
            String var8 = var6.group("varName");
            var9 = (String)var5.get(var8);
            if (var9 == null) {
               var2.add(new InvalidEnvVarInfo(var3, var8));
               var9 = "";
            }
         }

         var6.appendTail(var7);
         return var7.toString();
      }
   }

   private String a(String var1) {
      var1 = DVTFileUtils.a().h(var1);
      Path var2 = Paths.get(var1);
      if (var2 == null) {
         return null;
      } else if (var2.isAbsolute()) {
         return DVTFileUtils.a().i(var2.normalize().toString());
      } else {
         Path var3 = Utils.g();
         if (var3 == null) {
            return null;
         } else {
            Path var4 = Paths.get(var3.toString(), var1);
            return var4 == null ? null : DVTFileUtils.a().i(var4.normalize().toString());
         }
      }
   }

   private Path b() {
      Path var1 = Utils.g();
      if (var1 == null) {
         return null;
      } else {
         Path var2 = Paths.get(var1.toString(), AIUtils.c);
         return var2 != null && var2.toFile().isFile() ? var2 : null;
      }
   }

   private class InvalidEnvVarInfo {
      public int a;
      public String b;

      protected InvalidEnvVarInfo(int var2, String var3) {
         this.a = var2;
         this.b = var3;
      }
   }
}
