package ro.amiq.dvt.ai;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import ro.amiq.dvt.LanguageKind;
import ro.amiq.dvt.ai.contributor.AIContributorManager;
import ro.amiq.dvt.ai.contributor.IAIContributor;
import ro.amiq.dvt.ai.model.EditorPosition;
import ro.amiq.dvt.ai.model.SnippetSelectionType;
import ro.amiq.dvt.model.reflection.IRfDefElement;
import ro.amiq.dvt.model.reflection.IRfFileDef;
import ro.amiq.dvt.model.reflection.IRfNamedElement;
import ro.amiq.dvt.model.reflection.IRfNamedElementAndScope;
import ro.amiq.dvt.model.reflection.IRfScopeElement;
import ro.amiq.dvt.model.reflection.ParserPath;

public abstract class HotspotManagerCommon {
   private static final int a = 200;
   private static final int b = 50;
   private LinkedList c = new LinkedList();
   private LinkedList d = new LinkedList();

   public void a(EditorPosition var1) {
      if (var1 != null) {
         IRfNamedElementAndScope var2 = AIUtils.a().a(var1);
         if (var2 != null) {
            LanguageKind var3 = var2.getLanguageKind();
            IAIContributor var4 = AIContributorManager.INSTANCE.getContributor(var3);
            if (var4 != null) {
               IRfDefElement var5 = AIUtils.a().a(var2, var4.a(SnippetSelectionType.CONTAINER), false);
               if (var5 != null) {
                  this.a(var5);
               }
            }
         }
      }
   }

   public void a(String var1) {
      if (var1 != null) {
         String var2 = AIUtils.a().e(var1);
         this.c.remove(var2);
         this.c.addFirst(var2);
         if (this.c.size() > 50) {
            this.c.removeLast();
         }

      }
   }

   public boolean b(String var1) {
      for(String var2 : this.c) {
         if (var2.equals(var1)) {
            return true;
         }
      }

      return false;
   }

   private void a(IRfDefElement var1) {
      if (var1 != null && var1.getDefFile() != null) {
         this.d.remove(var1);
         this.d.addFirst(var1);
         if (this.d.size() > 200) {
            this.d.removeLast();
         }

      }
   }

   public List c() {
      ArrayList var1 = new ArrayList();

      for(IRfDefElement var2 : this.d) {
         IRfFileDef var4 = var2.getDefFile();
         if (var4 != null) {
            ParserPath var5 = var4.getParserPath();
            if (var5 != null && var5.path != null && this.b(AIUtils.a().e(var5.path)) && !AIProtectManager.INSTANCE.isFileProtected(var5.path)) {
               var1.add(var2);
            }
         }
      }

      return this.a((List)var1);
   }

   public List d() {
      ArrayList var1 = new ArrayList();

      for(IRfDefElement var2 : this.d) {
         IRfFileDef var4 = var2.getDefFile();
         if (var4 != null) {
            ParserPath var5 = var4.getParserPath();
            if (var5 != null && var5.path != null && !AIProtectManager.INSTANCE.isFileProtected(var5.path) && this.b().contains(AIUtils.a().e(var5.path))) {
               var1.add(var2);
            }
         }
      }

      return this.a((List)var1);
   }

   private List a(List var1) {
      if (var1.isEmpty()) {
         return var1;
      } else {
         LinkedHashMap var2 = new LinkedHashMap();

         for(IRfDefElement var3 : var1) {
            IRfNamedElement var5 = var3.getNamedElement();
            if (var5 == null) {
               var2.put(var3, (Object)null);
            } else if (!var2.containsValue(var5)) {
               var2.put(var3, var5);
            }
         }

         HashSet var6 = new HashSet(var2.values());
         return (List)var2.keySet().stream().filter((var2x) -> !this.a(var2x, var6)).collect(Collectors.toList());
      }
   }

   private boolean a(IRfDefElement var1, Set var2) {
      IRfNamedElement var3 = var1.getNamedElement();
      if (var3 == null) {
         return false;
      } else {
         for(IRfScopeElement var4 = var3.getEnclosingScope(); var4 != null; var4 = var4.getEnclosingScope()) {
            if (var2.contains(var4)) {
               return true;
            }
         }

         return false;
      }
   }

   protected abstract Set b();
}
