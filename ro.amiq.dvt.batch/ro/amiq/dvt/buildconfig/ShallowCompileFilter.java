package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.utils.DVTStringUtil;
import ro.amiq.dvt.utils.StringMatcher;

public class ShallowCompileFilter implements Serializable {
   private static final long serialVersionUID = 1L;
   private List shallowCompiles = new ArrayList();

   public void add(String var1, boolean var2, boolean var3, ShallowCompileMode var4) {
      if (var1 != null && !var1.isEmpty()) {
         if (var4 == ShallowCompileMode.FILE) {
            var1 = DVTStringUtil.n(var1);
            if (var1 == null || var1.isEmpty()) {
               return;
            }
         }

         this.shallowCompiles.add(new ShallowCompile(var1, var2, var3, var4));
      }
   }

   public void add(ShallowCompileFilter var1) {
      this.shallowCompiles.addAll(var1.shallowCompiles);
   }

   public boolean shouldShallow(String var1, String var2, ShallowCompileMode var3) {
      if (this.shallowCompiles.isEmpty()) {
         return false;
      } else {
         String var4 = DVTStringUtil.n(var1);
         String var5 = Path.fromOSString(var4).lastSegment();
         if (var5 == null || !var5.startsWith("__edt__") && !var5.startsWith("__sln__") && !var5.startsWith("__msdl__") && !var5.startsWith("__pss__") && !var5.startsWith("__vlog__") && !var5.startsWith("__vhdl__")) {
            boolean var6 = ((ShallowCompile)this.shallowCompiles.get(0)).allExcept;
            boolean var7 = false;
            boolean var8 = false;

            for(ShallowCompile var9 : this.shallowCompiles) {
               if ((var9.mode == ShallowCompileMode.FILE || var9.mode == var3) && (!var8 || var7 ^ !var9.allExcept)) {
                  String var11 = var9.mode == ShallowCompileMode.FILE ? var4 : var2;
                  boolean var12 = var9.pattern != null && var9.pattern.matcher(var11).matches();
                  var12 = var12 || var9.stringMatcher != null && var9.stringMatcher.match(var11);
                  if (var12) {
                     var8 = true;
                     var7 = !var9.allExcept;
                  }
               }
            }

            if (var6 && !var8) {
               return true;
            } else {
               return var7;
            }
         } else {
            return false;
         }
      }
   }

   public String toString() {
      StringBuilder var1 = new StringBuilder();

      for(ShallowCompile var2 : this.shallowCompiles) {
         var1.append("[");
         if (var2.allExcept) {
            var1.append("ALL_EXCEPT:");
         }

         if (var2.pattern != null) {
            var1.append(var2.pattern.pattern());
         } else {
            var1.append(var2.stringMatcher.toString());
         }

         var1.append("]");
      }

      return var1.toString();
   }

   public int hashCode() {
      int var1 = 1;
      var1 = 31 * var1 + (this.shallowCompiles == null ? 0 : this.shallowCompiles.hashCode());
      return var1;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!(var1 instanceof ShallowCompileFilter)) {
         return false;
      } else {
         ShallowCompileFilter var2 = (ShallowCompileFilter)var1;
         if (this.shallowCompiles == null) {
            if (var2.shallowCompiles != null) {
               return false;
            }
         } else if (!this.shallowCompiles.equals(var2.shallowCompiles)) {
            return false;
         }

         return true;
      }
   }

   private static class ShallowCompile implements Serializable {
      private static final long serialVersionUID = 1L;
      boolean allExcept;
      Pattern pattern;
      StringMatcher stringMatcher;
      ShallowCompileMode mode;

      ShallowCompile(String var1, boolean var2, boolean var3, ShallowCompileMode var4) {
         if (var2) {
            this.pattern = Pattern.compile(var1);
         } else {
            this.stringMatcher = new StringMatcher(var1, false, false, true);
         }

         this.allExcept = var3;
         this.mode = var4;
      }

      public int hashCode() {
         int var1 = 1;
         var1 = 31 * var1 + (this.allExcept ? 1231 : 1237);
         var1 = 31 * var1 + (this.mode == null ? 0 : this.mode.hashCode());
         var1 = 31 * var1 + (this.stringMatcher == null ? 0 : this.stringMatcher.hashCode());
         return var1;
      }

      public boolean equals(Object var1) {
         if (this == var1) {
            return true;
         } else if (!(var1 instanceof ShallowCompile)) {
            return false;
         } else {
            ShallowCompile var2 = (ShallowCompile)var1;
            if (this.allExcept != var2.allExcept) {
               return false;
            } else if (this.mode != var2.mode) {
               return false;
            } else {
               if (this.stringMatcher == null) {
                  if (var2.stringMatcher != null) {
                     return false;
                  }
               } else if (!this.stringMatcher.equals(var2.stringMatcher)) {
                  return false;
               }

               return true;
            }
         }
      }
   }
}
