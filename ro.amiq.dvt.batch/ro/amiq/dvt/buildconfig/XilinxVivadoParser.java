package ro.amiq.dvt.buildconfig;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import javax.xml.namespace.QName;
import javax.xml.xpath.XPathConstants;
import org.apache.commons.io.FilenameUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.startup.core.DVTLogger;

public class XilinxVivadoParser extends XilinxProjectConfigParser {
   private static final String v = "Found non existing verilog include directory: ";
   private static final String w = "Found verilog include directory: ";
   private static final String x = "Analyzing fileset: ";
   private static final String y = "Reading fileset config options";
   private static final String z = "Reading fileset defines";
   private static final String A = "Reading fileset generics and parameters";
   private static final String B = "Found block design file:\n    ";
   private static final String C = "Found included blocksrc file:\n    ";
   private static final String D = "Global include file:\n    ";
   private static final String E = "Verilog header file:\n    ";
   private static final String F = "Reading files in fileset";
   private static final String G = "No file nodes found in fileset";
   private static final String H = "Writing files for fileset: ";
   private static final String I = "File does not exist: ";
   private static final String J = "Found no fileset of type ";
   private static final String K = "Analyzing block fileset: ";
   private static final String L = "Analyzing included source files";
   private static final String M = "Null project element.";
   private static final String N = "Sources directory inside project not found...";
   private static final String O = "Project directory not found!";
   private static final String P = "No project configuration options found.";
   private static final String Q = "Found project path : ";
   private static final String R = "No Project element found in project file";
   private static final String S = "Found default library: ";
   private static final String T = "Found top entity: ";
   private static final String U = "Found active simulation fileset : ";
   private static final String V = "Found active design fileset : ";
   private static final String W = "Reading project configuration";
   private static final String X = ".xml";
   private static final String Y = ".bd";
   private static final String Z = ".bxml";
   private static final String aa = ".xci";
   private static final String ab = "Project";
   private static final String ac = "Path";
   private static final String ad = "File";
   private static final String ae = "FileInfo";
   private static final String af = "Option";
   private static final String ag = "Attr";
   private static final String ah = "Define";
   private static final String ai = "Generic";
   private static final String aj = "Name";
   private static final String ak = "Val";
   private static final String al = "Type";
   private static final String am = "UserDisabled";
   private static final String an = "IsGlobalInclude";
   private static final String ao = "1";
   private static final String ap = "SFType";
   private static final String aq = "Library";
   private static final String ar = "Config";
   private static final String as = "RelSrcDir";
   private static final String at = "VerilogDir";
   private static final String au = "SrcSet";
   private static final String av = "sources_1";
   private static final String aw = "DesignSrcs";
   private static final String ax = "SimulationSrcs";
   private static final String ay = "BlockSrcs";
   private static final String az = "DefaultLib";
   private static final String aA = "TopModule";
   private static final String aB = "TopLib";
   private static final String aC = "ActiveSimSet";
   private static final String aD = "/Project/Configuration/Option[@Name = 'SimulatorLanguage']/@Val";
   private static final String aE = "VHDL";
   private static final String aF = "Verilog";
   private static final String aG = ".srcs";
   private static final String aH = "$PPRDIR";
   private static final String aI = "$PSRCDIR";
   private static final String aJ = "work";
   private static final String aK = "Configuration/Option";
   public String u = "work";
   private String aL = null;
   private String aM = null;
   private File aN = null;
   private File aO = null;
   private Set aP;
   private Element aQ;
   private Map aR;

   public XilinxVivadoParser(File var1, String var2, BuildConfigParser.AutoConfigParameters var3, DVTBuildConsole var4, IProgressMonitor var5) throws Exception {
      super(var1, var2, var3, var4, var5);
      this.aN = var1.getParentFile();
      if (var3 == null || this.aN.exists() && this.aN.isDirectory()) {
         this.aO = new File(this.aN, FilenameUtils.getBaseName(var1.getAbsolutePath()) + ".srcs");
         if (var3 != null && (!this.aO.exists() || !this.aO.isDirectory())) {
            var4.a("Sources directory inside project not found...");
         }

         this.aR = new HashMap();
         this.aP = new LinkedHashSet();
      } else {
         throw new Exception("Project directory not found!");
      }
   }

   public void b() throws Exception {
      this.g.d("Reading project configuration");
      Object var1 = this.a((String)"Project", (Object)this.e, (QName)XPathConstants.NODE);
      if (!(var1 instanceof Element)) {
         this.g.a("No Project element found in project file");
         throw new Exception("No Project element found in project file");
      } else {
         this.aQ = (Element)var1;
         String var2 = this.aQ.getAttribute("Path");
         if (var2 != null && !var2.isEmpty()) {
            File var3 = new File(var2);
            if (var3.exists() && var3.isDirectory()) {
               this.g.a("Found project path : " + var3.getCanonicalPath());
               this.aN = var3;
            }
         }

         try {
            NodeList var9 = (NodeList)this.a((String)"Configuration/Option", (Object)this.aQ, (QName)XPathConstants.NODESET);
            if (var9 == null) {
               this.g.a("No project configuration options found.");
               return;
            }

            for(int var4 = 0; var4 < var9.getLength(); ++var4) {
               if (this.i.isCanceled()) {
                  return;
               }

               Element var5 = (Element)var9.item(var4);
               String var6 = var5.getAttribute("Name");
               String var7 = var5.getAttribute("Val");
               if (!var6.isEmpty() && !var7.isEmpty()) {
                  if ("DefaultLib".equals(var6)) {
                     this.u = var7;
                     this.g.a("Found default library: " + this.u);
                  } else if ("ActiveSimSet".equals(var6) && (this.aL == null || this.aL.isEmpty())) {
                     this.aL = var7;
                     this.g.a("Found active simulation fileset : " + this.aL);
                  }
               }
            }
         } catch (Exception var8) {
            DVTLogger.INSTANCE.logError("Exception while parsing project configuration: ", var8);
         }

      }
   }

   public void c() throws Exception {
      if (this.aQ == null) {
         throw new Exception("Null project element.");
      } else {
         this.g.d("Analyzing included source files");
         FileSetInfo var1 = this.a(this.aL, "SimulationSrcs");
         if (var1 != null && !var1.a()) {
            this.aR.put(this.aL, var1);
         }

         this.aM = this.aM != null ? this.aM : "sources_1";
         FileSetInfo var2 = this.a(this.aM, "DesignSrcs");
         if (var2 != null && !var2.a()) {
            this.aR.put(this.aM, var2);
         }

         this.g();
      }
   }

   private void g() throws Exception {
      Object var1 = this.a((String)"FileSets/FileSet[ @Type = 'BlockSrcs']", (Object)this.aQ, (QName)XPathConstants.NODESET);
      if (!(var1 instanceof NodeList)) {
         this.g.a("Found no fileset of type BlockSrcs");
      } else {
         NodeList var2 = (NodeList)var1;

         for(int var3 = 0; var3 < var2.getLength(); ++var3) {
            try {
               Node var4 = var2.item(var3);
               if (var4 instanceof Element) {
                  Element var5 = (Element)var4;
                  String var6 = var5.getAttribute("Name");
                  this.g.a("Analyzing block fileset: " + var6);
                  FileSetInfo var7 = new FileSetInfo();
                  String var8 = var5.getAttribute("RelSrcDir");
                  if (!var8.isEmpty()) {
                     var7.a = this.b(var8, "");
                  } else {
                     var7.a = "";
                  }

                  this.b(var5, var7, var8);
                  this.a(var5, var7, var8);
                  this.aR.put(var6, var7);
               }
            } catch (Exception var9) {
               DVTLogger.INSTANCE.logError("Exception while parsing block fileset: ", var9);
            }
         }

      }
   }

   private FileSetInfo a(String var1, String var2) throws Exception {
      try {
         FileSetInfo var3 = new FileSetInfo();
         Object var4 = this.a((String)("FileSets/FileSet[@Name = '" + var1 + "' and @Type = '" + var2 + "']"), (Object)this.aQ, (QName)XPathConstants.NODE);
         if (!(var4 instanceof Element)) {
            this.g.a("Found no fileset with name '" + var1 + "' and of type " + var2);
            return var3;
         } else {
            this.g.a("Analyzing fileset: " + var1);
            Element var5 = (Element)var4;
            String var6 = var5.getAttribute("RelSrcDir");
            if (!var6.isEmpty()) {
               var3.a = this.b(var6, "");
            } else {
               var3.a = "";
            }

            this.b(var5, var3, var6);
            this.a(var5, var3, var6);
            return var3;
         }
      } catch (Exception var7) {
         DVTLogger.INSTANCE.logError("Exception while parsing fileset: " + var1, var7);
         return null;
      }
   }

   private void a(Element var1, FileSetInfo var2, String var3) throws Exception {
      NodeList var4 = var1.getElementsByTagName("File");
      if (var4 == null) {
         this.g.a("No file nodes found in fileset");
      } else {
         this.g.a("Reading files in fileset");

         label87:
         for(int var5 = 0; var5 < var4.getLength(); ++var5) {
            try {
               if (this.i.isCanceled()) {
                  return;
               }

               Element var6 = (Element)var4.item(var5);
               String var7 = var6.getAttribute("Path");
               if (!var7.isEmpty()) {
                  var7 = FilenameUtils.separatorsToSystem(var7);
                  var7 = this.b(var7, var3);
                  if (var7 != null) {
                     File var8 = new File(var7);
                     if (!var8.exists()) {
                        DVTLogger.INSTANCE.logError("File does not exist: " + var7);
                     } else {
                        Node var9 = (Node)this.a((String)"FileInfo", (Object)var6, (QName)XPathConstants.NODE);
                        if (var9 instanceof Element) {
                           Element var10 = (Element)var9;
                           String var11 = var10.getAttribute("SFType");
                           VivadoFileTypes var12 = XilinxVivadoParser.VivadoFileTypes.a(var11, "." + FilenameUtils.getExtension(var8.getName()));
                           if (var12 != null) {
                              if (XilinxVivadoParser.VivadoFileTypes.VERILOG_HEADER_FILE.equals(var12)) {
                                 this.g.a("Verilog header file:\n    " + var7);
                                 this.aP.add(var8.getParent());
                              } else {
                                 String var13 = this.u;
                                 NodeList var14 = var10.getElementsByTagName("Attr");

                                 for(int var15 = 0; var15 < var14.getLength(); ++var15) {
                                    Element var16 = (Element)var14.item(var15);
                                    String var17 = var16.getAttribute("Name");
                                    String var18 = var16.getAttribute("Val");
                                    if ("Library".equals(var17) && !var18.isEmpty()) {
                                       var13 = var18;
                                    }

                                    if ("IsGlobalInclude".equals(var17) && "1".equals(var18)) {
                                       this.g.a("Global include file:\n    " + var7);
                                       this.aP.add(var7);
                                       continue label87;
                                    }
                                 }

                                 if (XilinxVivadoParser.VivadoFileTypes.IP_INCLUDE.equals(var12)) {
                                    this.a(var2, var7, var13);
                                 } else if (XilinxVivadoParser.VivadoFileTypes.BLOCK_DESIGN.equals(var12)) {
                                    this.a(var2, var7);
                                 } else {
                                    this.a(var2.b, var7, var13, var12.dvtSyntaxName);
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            } catch (Exception var19) {
               DVTLogger.INSTANCE.logError("Exception while parsing file: ", var19);
            }
         }

      }
   }

   private void a(FileSetInfo var1, String var2, String var3) throws Exception {
      Path var4 = Paths.get(var2.replace(".xci", ".xml"));
      File var5 = var4.toFile();
      if (var5.exists()) {
         this.g.a("Found included blocksrc file:\n    " + var2);
         XilinxIPParser var6 = new XilinxIPParser(var5, this.f.toString(), var3, this.g, this.i);
         XilinxIPParser.ParseResult var7 = var6.b();
         if (!this.i.isCanceled()) {
            this.aP.addAll(var7.b);

            for(Map.Entry var8 : var7.a.entrySet()) {
               String var10 = (String)var8.getKey();
               XilinxConfigParser.XilinxFilesContainer var11 = (XilinxConfigParser.XilinxFilesContainer)var8.getValue();

               for(Map.Entry var12 : var11.a.entrySet()) {
                  String var14 = (String)var12.getKey();
                  Collection var15 = (Collection)var12.getValue();
                  this.a(var1.b, var15, var10, var14);
               }
            }

         }
      }
   }

   private void a(FileSetInfo var1, String var2) throws Exception {
      String var3 = (new File(var2)).getParent();
      File var4 = new File(var2.replace(".bd", ".bxml"));
      if (var4.exists()) {
         this.g.a("Found block design file:\n    " + var2);
         XilinxBXMLParser var5 = new XilinxBXMLParser(var4, var3, this.g, this.i);
         Map var6 = var5.b();

         for(Map.Entry var7 : var6.entrySet()) {
            if (this.i.isCanceled()) {
               return;
            }

            String var9 = (String)var7.getKey();
            String var10 = (String)var7.getValue();
            VivadoFileTypes var11 = XilinxVivadoParser.VivadoFileTypes.a((String)null, "." + FilenameUtils.getExtension(var9));
            if (var11 != null && var11 != XilinxVivadoParser.VivadoFileTypes.BLOCK_DESIGN && var11 != XilinxVivadoParser.VivadoFileTypes.IP_INCLUDE) {
               this.a(var1.b, (String)var7.getKey(), (String)var7.getValue(), var11.dvtSyntaxName);
            } else {
               this.a(var1, var9, var10);
            }
         }

      }
   }

   private void b(Element var1, FileSetInfo var2, String var3) throws Exception {
      this.a(var1, var3);
      this.b(var1, var2);
      this.a(var1, var2);
   }

   private void a(Element var1, FileSetInfo var2) throws Exception {
      NodeList var3 = (NodeList)this.a((String)"Config/Generic", (Object)var1, (QName)XPathConstants.NODESET);
      if (var3 != null) {
         this.g.a("Reading fileset generics and parameters");

         for(int var4 = 0; var4 < var3.getLength(); ++var4) {
            if (this.i.isCanceled()) {
               return;
            }

            Element var5 = (Element)var3.item(var4);
            String var6 = var5.getAttribute("Name");
            String var7 = var5.getAttribute("Val");
            if (!var6.isEmpty()) {
               var2.d.put(var6, var7);
            }
         }

      }
   }

   private void b(Element var1, FileSetInfo var2) throws Exception {
      NodeList var3 = (NodeList)this.a((String)"Config/Define", (Object)var1, (QName)XPathConstants.NODESET);
      if (var3 != null) {
         this.g.a("Reading fileset defines");

         for(int var4 = 0; var4 < var3.getLength(); ++var4) {
            if (this.i.isCanceled()) {
               return;
            }

            Element var5 = (Element)var3.item(var4);
            String var6 = var5.getAttribute("Name");
            String var7 = var5.getAttribute("Val");
            if (!var6.isEmpty()) {
               var2.c.put(var6, var7);
            }
         }

      }
   }

   private void a(Element var1, String var2) throws Exception {
      NodeList var3 = (NodeList)this.a((String)"Config/Option", (Object)var1, (QName)XPathConstants.NODESET);
      if (var3 != null) {
         this.g.a("Reading fileset config options");

         for(int var4 = 0; var4 < var3.getLength(); ++var4) {
            if (this.i.isCanceled()) {
               return;
            }

            Element var5 = (Element)var3.item(var4);
            String var6 = var5.getAttribute("Name");
            String var7 = var5.getAttribute("Val");
            if (!var6.isEmpty() && !var7.isEmpty()) {
               if ("TopModule".equals(var6)) {
                  if (!this.s.contains("-top " + var7 + h)) {
                     this.s = "-top " + var7 + h + this.s;
                  }

                  this.g.a("Found top entity: " + var7);
               } else if ("SrcSet".equals(var6)) {
                  this.aM = var7;
                  this.g.a("Found active design fileset : " + var7);
               } else if ("VerilogDir".equals(var6)) {
                  String var8 = this.b(var7, var2);
                  if (!(new File(var8)).exists()) {
                     this.g.a("Found non existing verilog include directory: " + var8);
                  } else {
                     if (var8 != null) {
                        this.aP.add(var8);
                     }

                     this.g.a("Found verilog include directory: " + var8);
                  }
               }
            }
         }

      }
   }

   private String b(String var1, String var2) throws Exception {
      if (var1 != null && !var1.isEmpty()) {
         if (this.aN != null) {
            var1 = var1.replace("$PPRDIR", this.aN.getCanonicalPath());
         }

         if (this.aO != null) {
            var1 = var1.replace("$PSRCDIR", this.aO.getCanonicalPath());
         }

         File var3 = new File(var1);
         if (!var3.isAbsolute()) {
            var3 = new File(var2, var1);
         }

         return var3.getCanonicalPath();
      } else {
         return null;
      }
   }

   protected AutoConfigResult d() throws Exception {
      if (this.i.isCanceled()) {
         return new AutoConfigResult(AutoConfigResult.Status.CANCELED);
      } else {
         AutoConfigResult var1 = new AutoConfigResult(AutoConfigResult.Status.EMPTY);
         if (!this.s.isEmpty()) {
            var1.a((CharSequence)(this.s + h));
         }

         for(Map.Entry var2 : this.aR.entrySet()) {
            String var4 = (String)var2.getKey();
            this.g.a("Writing files for fileset: " + var4);
            FileSetInfo var5 = (FileSetInfo)var2.getValue();
            if (var5 != null && !var5.a()) {
               var1.a(this.a((Set)this.aP, (Map)var5.c, (Map)var5.b));
            }
         }

         return var1;
      }
   }

   public void b(String var1) {
      this.aL = var1;
   }

   public String[] e() throws Exception {
      String var1 = (String)this.a((String)"/Project/Configuration/Option[@Name = 'SimulatorLanguage']/@Val", (Object)this.e, (QName)XPathConstants.STRING);
      if (var1 != null && !var1.isEmpty()) {
         if ("VHDL".equals(var1)) {
            this.q = true;
            return new String[]{"ro.amiq.vhdldt.VhdlNature"};
         } else if ("Verilog".equals(var1)) {
            this.r = true;
            return new String[]{"ro.amiq.vlogdt.VlogNature"};
         } else {
            return null;
         }
      } else {
         this.q = true;
         this.r = true;
         return new String[]{"ro.amiq.vlogdt.VlogNature", "ro.amiq.vhdldt.VhdlNature"};
      }
   }

   protected String a(String var1) {
      return XilinxVivadoParser.VivadoFileTypes.getCompatModeForSyntax(var1);
   }

   static enum VivadoFileTypes {
      VHDL_FILE("VHDL", "vhdlSource", "VHDL_2002", "vcom", new String[]{".vhd", ".vhdl"}),
      VHDL_2008_FILE("VHDL2008", "vhdlSource-2008", "VHDL_2008", "vcom", new String[]{".vhd", ".vhdl"}),
      VERILOG_FILE("Verilog", "verilogSource", "Verilog_2005", "vlog", new String[]{".v"}),
      SYSTEM_VERILOG_FILE("SVerilog", "systemVerilogSource", "SystemVerilog", "vlog", new String[]{".sv"}),
      VERILOG_HEADER_FILE("VHeader", "verilogSource", "Verilog_2005", "vlog", new String[]{".vh", ".svh"}),
      BLOCK_DESIGN("BD", "", "", "", new String[]{".bd"}),
      IP_INCLUDE("XCI", "xml", "", "", new String[]{".xci"});

      private String typeName;
      private String typeNameInIPFile;
      private Set extensions;
      private String questaCompatMode;
      public String dvtSyntaxName;

      private VivadoFileTypes(String var3, String var4, String var5, String var6, String... var7) {
         this.typeName = var3;
         this.typeNameInIPFile = var4;
         this.dvtSyntaxName = var5;
         this.questaCompatMode = var6;
         this.extensions = new HashSet(Arrays.asList(var7));
      }

      private static VivadoFileTypes a(String var0, String var1) {
         if (var0 != null && !var0.isEmpty()) {
            return b(var0);
         } else {
            return var1 != null ? a(var1) : null;
         }
      }

      private static VivadoFileTypes a(String var0) {
         VivadoFileTypes[] var4;
         for(VivadoFileTypes var1 : var4 = values()) {
            if (var1.extensions.contains(var0)) {
               return var1;
            }
         }

         return null;
      }

      private static VivadoFileTypes b(String var0) {
         VivadoFileTypes[] var4;
         for(VivadoFileTypes var1 : var4 = values()) {
            if (var1.typeName.equals(var0)) {
               return var1;
            }
         }

         return null;
      }

      public static VivadoFileTypes getFileTypeFromIPFileType(String var0) {
         if (var0 == null) {
            return null;
         } else {
            VivadoFileTypes[] var4;
            for(VivadoFileTypes var1 : var4 = values()) {
               if (var1.typeNameInIPFile.equals(var0)) {
                  return var1;
               }
            }

            return null;
         }
      }

      public static String getCompatModeForSyntax(String var0) {
         if (var0 != null && !var0.isEmpty()) {
            VivadoFileTypes[] var4;
            for(VivadoFileTypes var1 : var4 = values()) {
               if (var1.dvtSyntaxName.equals(var0)) {
                  return "+questa." + var1.questaCompatMode + " ";
               }
            }

            return " ";
         } else {
            return " ";
         }
      }
   }

   protected static class FileSetInfo {
      protected String a;
      protected Map b = new LinkedHashMap();
      protected Map c = new LinkedHashMap();
      protected Map d = new LinkedHashMap();

      public FileSetInfo() {
      }

      public boolean a() {
         return this.b.isEmpty();
      }
   }
}
