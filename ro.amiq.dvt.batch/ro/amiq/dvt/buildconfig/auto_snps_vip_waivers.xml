<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE waivers PUBLIC "-//DVT//waivers" "waivers.dtd" >
<waivers version="1">

<!-- Don't care about errors inside SVT -->
	<waiver name="EXTEND_CLASS" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="EXTEND_CLASS: Extending non existing class '*'"/>
	</waiver>
	<waiver name="UNDECLARED_IDENTIFIER" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="UNDECLARED_IDENTIFIER: Identifier '*' is not declared"/>
	</waiver>
	<waiver name="NON_EXISTING_TYPE" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="NON_EXISTING_TYPE: Non existing type '*'"/>
	</waiver>
	<waiver name="UNDECLARED_IDENTIFIER" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="UNDECLARED_IDENTIFIER: Identifier '*' is not a member of '*' (type '*' is undeclared)"/>
	</waiver>
	<waiver name="UNDECLARED_IDENTIFIER" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="UNDECLARED_IDENTIFIER: Identifier '*' is not a member of '*'"/>
	</waiver>
	<waiver name="EXTERN_TASK_UNDECLARED" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="EXTERN_TASK_UNDECLARED: Outer class task '*' was not declared extern inside class '*'"/>
	</waiver>
	<waiver name="MISSING_FUNCTION_IMPL" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="MISSING_FUNCTION_IMPLEMENTATION: '*' extern function is not implemented"/>
	</waiver>
	<waiver name="MISSING_TASK_IMPL" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="MISSING_TASK_IMPLEMENTATION: '*' extern task is not implemented"/>
	</waiver>
	<waiver name="SYSTEM_VERILOG_2012" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="*Macro name: * is not defined"/>
	</waiver>
	<waiver name="SYSTEM_VERILOG_2012_WREAL" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="*Redefinition of macro name: *"/>
	</waiver>
	<waiver name="SYSTEM_VERILOG_2012" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="*NON_STANDARD: Empty assignment pattern '{} not allowed"/>
	</waiver>
	<waiver name="SYSTEM_VERILOG_2012" severity="DISABLED">
		<match path="*/synopsys_vip_*/*" message="*NON_STANDARD: Function prototype return data type or void missing"/>
	</waiver>

</waivers>
