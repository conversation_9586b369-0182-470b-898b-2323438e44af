package ro.amiq.dvt.buildconfig.logsimulator;

import com.google.common.collect.ImmutableMap;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.buildconfig.BuildConfigParser;

public enum XRunLogSimulatorParser implements LogSimulatorParser {
   INSTANCE;

   private static final String c = " ([A-Z]{3,4})";
   private static final String d = "Design Systems, Inc.";
   private static final List e = Collections.unmodifiableList(Arrays.asList(Pattern.compile("(( ([A-Z]{3,4})|Design Systems, Inc.)\\s+xrun\\s)(?<args>.*?)(\\s+(xrun:|User|The))", 32)));
   private static final Map f = ImmutableMap.of(Pattern.compile("\t\t+.*[\n\r]+"), "", Pattern.compile("(-define\\s*\\w+=)\"(.*?)\""), "$1\\\\\"$2\\\\\"", Pattern.compile("(\\+define\\+\\w+=)\"(.*?)\""), "$1\\\\\"$2\\\\\"");

   public LogSimulatorResult parseLogFile(LogSimulatorResult var1, Path var2, BuildConfigParser.SimLogConfigParameters var3, IProgressMonitor var4) {
      this.a(var1, var2, var4, e, var3.maxSizeBuffer, var3.fullParsing);
      return var4.isCanceled() ? LogSimulatorResult.a : var1;
   }

   public List getExtractionPatterns() {
      return e;
   }

   public Map getReplacementPatterns() {
      return f;
   }
}
