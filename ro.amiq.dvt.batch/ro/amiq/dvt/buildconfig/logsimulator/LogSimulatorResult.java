package ro.amiq.dvt.buildconfig.logsimulator;

import java.util.ArrayList;
import java.util.List;
import org.eclipse.core.runtime.IPath;

public class LogSimulatorResult {
   private static final String c = System.lineSeparator();
   public static final LogSimulatorResult a;
   public static final LogSimulatorResult b;
   private Status d;
   private List e;
   private List f;
   private List g;
   private int h;

   static {
      a = new LogSimulatorResult(LogSimulatorResult.Status.CANCELED);
      b = new LogSimulatorResult(LogSimulatorResult.Status.EMPTY);
   }

   private LogSimulatorResult(Status var1) {
      this.d = var1;
   }

   public LogSimulatorResult() {
      this.d = LogSimulatorResult.Status.EMPTY;
      this.e = new ArrayList();
      this.f = new ArrayList();
      this.h = -1;
      this.g = new ArrayList();
   }

   public Status a() {
      return this.d;
   }

   public void a(Status var1) {
      this.d = var1;
   }

   public void a(int var1, String var2) {
      if (var1 >= 0 && var1 < this.e.size() && var2 != null) {
         ((StringBuffer)this.e.get(var1)).insert(0, var2);
      }
   }

   public void a(String var1) {
      this.a(this.h, var1);
   }

   public void b(int var1, String var2) {
      if (var1 >= 0 && var1 < this.e.size() && var2 != null) {
         ((StringBuffer)this.e.get(var1)).append(var2).append(c);
      }
   }

   public void b(String var1) {
      this.b(this.h, var1);
   }

   public void c(String var1) {
      if (var1 != null) {
         this.g.add(var1);
      }
   }

   public StringBuffer a(int var1) {
      return (StringBuffer)this.e.get(var1);
   }

   public IPath b(int var1) {
      return var1 >= 0 && var1 < this.f.size() ? (IPath)this.f.get(var1) : null;
   }

   public void a(int var1, StringBuffer var2) {
      this.e.set(var1, var2);
   }

   public void b() {
      if (this.d != LogSimulatorResult.Status.CANCELED) {
         if (this.d()) {
            this.d = LogSimulatorResult.Status.ERROR;
         } else {
            boolean var1 = true;

            for(StringBuffer var2 : this.e) {
               if (var2.length() != 0) {
                  var1 = false;
                  break;
               }
            }

            if (var1) {
               this.d = LogSimulatorResult.Status.EMPTY;
            } else {
               this.d = LogSimulatorResult.Status.OK;
            }
         }
      }
   }

   public List c() {
      return this.g;
   }

   public boolean d() {
      return !this.g.isEmpty();
   }

   public void a(IPath var1) {
      ++this.h;
      this.f.add(var1);
      this.e.add(new StringBuffer());
   }

   public int e() {
      return this.h + 1;
   }

   public String f() {
      StringBuilder var1 = new StringBuilder();
      this.e.forEach((var1x) -> var1.append(var1x).append(c));
      return var1.toString();
   }

   public static enum Status {
      EMPTY,
      OK,
      ERROR,
      CANCELED,
      TIMEOUT;
   }
}
