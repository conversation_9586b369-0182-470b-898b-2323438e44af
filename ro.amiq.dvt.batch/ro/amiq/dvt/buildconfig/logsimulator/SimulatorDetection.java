package ro.amiq.dvt.buildconfig.logsimulator;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.buildconfig.BuildConfigParser;
import ro.amiq.dvt.buildconfig.IBuildConfigParserConstants;
import ro.amiq.dvt.buildconfig.Invocation;
import ro.amiq.dvt.startup.core.DVTLogger;

public enum SimulatorDetection {
   INSTANCE;

   public SimulatorType getDefaultSimulator() {
      return SimulatorType.VCS;
   }

   public SimulatorType getSimulator(Invocation var1, BuildConfigParser.SimLogConfigParameters var2, IProgressMonitor var3) {
      Object var4 = null;
      SimulatorType var5 = this.getUserSpecifiedSimulatorType(var1);
      if (var5 != null) {
         return var5;
      } else {
         var5 = this.a(var2, var3);
         return var5 != null ? var5 : null;
      }
   }

   public SimulatorType getUserSpecifiedSimulatorType(Invocation var1) {
      IBuildConfigParserConstants.ToolCompat var2 = var1.getState().fToolCompat;
      if (var2 == IBuildConfigParserConstants.ToolCompat.IUS_IRUN) {
         return SimulatorType.IRUN;
      } else if (var2 == IBuildConfigParserConstants.ToolCompat.XCELIUM_XRUN) {
         return SimulatorType.XRUN;
      } else if (var2 != IBuildConfigParserConstants.ToolCompat.VCS_VHDLAN && var2 != IBuildConfigParserConstants.ToolCompat.VCS_VLOGAN) {
         return var2 != IBuildConfigParserConstants.ToolCompat.QUESTA_VCOM && var2 != IBuildConfigParserConstants.ToolCompat.QUESTA_VLOG ? null : SimulatorType.QUESTA;
      } else {
         return SimulatorType.VCS;
      }
   }

   private SimulatorType a(BuildConfigParser.SimLogConfigParameters var1, IProgressMonitor var2) {
      Path var3 = (Path)var1.simLogFiles.get(0);
      if (var3 == null) {
         return null;
      } else {
         String var4 = var3.toOSString();

         SimulatorType[] var8;
         for(SimulatorType var5 : var8 = SimulatorType.values()) {
            if (this.a(var4, var2, var5.getDetectionPattern(), var1.maxSizeBuffer, var1.fullParsing)) {
               return var5;
            }
         }

         return null;
      }
   }

   private boolean a(String var1, IProgressMonitor var2, Pattern var3, long var4, boolean var6) {
      long var7 = 0L;

      try {
         Throwable var9 = null;
         Object var10 = null;

         try {
            BufferedReader var11 = new BufferedReader(new FileReader(var1));

            try {
               Object var12 = null;

               while((var26 = var11.readLine()) != null) {
                  if (var2.isCanceled()) {
                     return false;
                  }

                  var7 += (long)var26.length();
                  if (!var6 && var7 >= var4) {
                     return false;
                  }

                  Matcher var13 = var3.matcher(new CancellableCharSequence(var26, var2));
                  if (var13.find()) {
                     return true;
                  }
               }

               return false;
            } finally {
               if (var11 != null) {
                  var11.close();
               }

            }
         } catch (Throwable var23) {
            if (var9 == null) {
               var9 = var23;
            } else if (var9 != var23) {
               var9.addSuppressed(var23);
            }

            throw var9;
         }
      } catch (FileNotFoundException var24) {
         DVTLogger.INSTANCE.logError((Throwable)var24);
      } catch (IOException var25) {
         DVTLogger.INSTANCE.logError((Throwable)var25);
      }

      return false;
   }
}
