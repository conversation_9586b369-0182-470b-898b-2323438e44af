package ro.amiq.dvt.buildconfig.logsimulator;

import java.util.EnumSet;
import java.util.regex.Pattern;
import ro.amiq.dvt.buildconfig.IBuildConfigParserConstants;

public enum SimulatorType {
   VCS(VCSLogSimulatorParser.INSTANCE, SimulatorType.Constants.a, IBuildConfigParserConstants.ey),
   IRUN(IRunLogSimulatorParser.INSTANCE, SimulatorType.Constants.c, EnumSet.of(IBuildConfigParserConstants.ToolCompat.IUS_IRUN)),
   XRUN(XRunLogSimulatorParser.INSTANCE, SimulatorType.Constants.b, EnumSet.of(IBuildConfigParserConstants.ToolCompat.XCELIUM_XRUN)),
   QUESTA(QuestaLogSimulatorParser.INSTANCE, SimulatorType.Constants.d, IBuildConfigParserConstants.eA);

   private final LogSimulatorParser parser;
   private final Pattern detectionPattern;
   private final EnumSet toolCompats;

   private SimulatorType(LogSimulatorParser var3, Pattern var4, EnumSet var5) {
      this.parser = var3;
      this.detectionPattern = var4;
      this.toolCompats = var5;
   }

   public LogSimulatorParser getParser() {
      return this.parser;
   }

   public Pattern getDetectionPattern() {
      return this.detectionPattern;
   }

   public IBuildConfigParserConstants.ToolCompat getToolCompat() {
      return (IBuildConfigParserConstants.ToolCompat)this.toolCompats.iterator().next();
   }

   public static SimulatorType from(String var0) {
      if (var0 != null && !(var0 = var0.trim()).isEmpty()) {
         SimulatorType[] var4;
         for(SimulatorType var1 : var4 = values()) {
            if (var1.name().equalsIgnoreCase(var0)) {
               return var1;
            }
         }

         return null;
      } else {
         return null;
      }
   }

   private static class Constants {
      public static final Pattern a = Pattern.compile("\\bvcs\\b", 2);
      public static final Pattern b = Pattern.compile("\\bxrun\\b", 2);
      public static final Pattern c = Pattern.compile("\\birun\\b", 2);
      public static final Pattern d = Pattern.compile("\\b(vcom|vlog)\\b", 2);
   }
}
