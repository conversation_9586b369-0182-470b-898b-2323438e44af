package ro.amiq.dvt.buildconfig.logsimulator;

import java.io.BufferedReader;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.buildconfig.BuildConfigParser;
import ro.amiq.dvt.startup.core.DVTLogger;

public interface LogSimulatorParser {
   String a = "args";
   int b = 250000;

   LogSimulatorResult parseLogFile(LogSimulatorResult var1, Path var2, BuildConfigParser.SimLogConfigParameters var3, IProgressMonitor var4);

   List getExtractionPatterns();

   Map getReplacementPatterns();

   default void a(LogSimulatorResult var1, IPath var2, IProgressMonitor var3, List var4, long var5, boolean var7) {
      String var8 = var2.toOSString();

      try {
         Throwable var9 = null;
         Object var10 = null;

         try {
            BufferedReader var11 = new BufferedReader(new FileReader(var8));

            try {
               char[] var12 = new char[250000];
               StringBuilder var13 = new StringBuilder();

               while(var11.read(var12) != -1) {
                  var13.append(var12);
                  if (var3.isCanceled()) {
                     return;
                  }

                  if (!var7 && (long)var13.length() >= var5) {
                     var1.c("# The parsing log file process stopped after reading " + var13.length() + "bytes, try to increase the max size of buffer using +dvt_simlog_max_size_buffer+<size in MB>.");
                     return;
                  }

                  int var14 = this.a(var2, var4, var13, var1, var7, var3);
                  if (var14 == -1) {
                     return;
                  }

                  if (var14 != 0) {
                     if (var13.length() < var14) {
                        var13.setLength(0);
                     } else {
                        var13.delete(0, var14);
                     }
                  }
               }

               return;
            } finally {
               if (var11 != null) {
                  var11.close();
               }

            }
         } catch (Throwable var24) {
            if (var9 == null) {
               var9 = var24;
            } else if (var9 != var24) {
               var9.addSuppressed(var24);
            }

            throw var9;
         }
      } catch (FileNotFoundException var25) {
         DVTLogger.INSTANCE.logError((Throwable)var25);
      } catch (IOException var26) {
         DVTLogger.INSTANCE.logError((Throwable)var26);
      }

   }

   default int a(IPath var1, List var2, StringBuilder var3, LogSimulatorResult var4, boolean var5, IProgressMonitor var6) {
      for(Pattern var7 : var2) {
         Matcher var9 = var7.matcher(new CancellableCharSequence(var3, var6));
         int var10 = 0;

         while(var9.find()) {
            var4.a(var1);
            var4.b(var9.group("args"));
            var10 = var9.end();
            if (!var5) {
               return -1;
            }
         }

         if (var10 != 0) {
            return var10;
         }
      }

      return 0;
   }

   default LogSimulatorResult a(LogSimulatorResult var1, Path var2, BuildConfigParser.SimLogConfigParameters var3, IProgressMonitor var4) {
      this.a(var1, var2, var4, var3.userSpecifiedPatterns, var3.maxSizeBuffer, var3.fullParsing);
      return var4.isCanceled() ? LogSimulatorResult.a : var1;
   }
}
