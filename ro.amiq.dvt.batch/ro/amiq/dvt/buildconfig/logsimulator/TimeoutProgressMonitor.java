package ro.amiq.dvt.buildconfig.logsimulator;

import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.ProgressMonitorWrapper;

public class TimeoutProgressMonitor extends ProgressMonitorWrapper {
   private long a;
   private String b;
   private boolean c;

   protected TimeoutProgressMonitor(IProgressMonitor var1, int var2) {
      super(var1);
      this.a = var2 == 0 ? Long.MAX_VALUE : System.currentTimeMillis() + (long)(var2 * 1000);
   }

   public boolean isCanceled() {
      this.c = System.currentTimeMillis() > this.a;
      return this.c || super.isCanceled();
   }

   public void setTaskName(String var1) {
      super.setTaskName(var1);
      this.b = var1;
   }

   public String a() {
      return this.b;
   }

   public boolean b() {
      return this.c;
   }
}
