package ro.amiq.dvt.buildconfig.logsimulator;

import com.google.common.collect.ImmutableMap;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.buildconfig.BuildConfigParser;

public enum IRunLogSimulatorParser implements LogSimulatorParser {
   INSTANCE;

   private static final String c = " ([A-Z]{3,4})";
   private static final List d = Collections.unmodifiableList(Arrays.asList(Pattern.compile("( ([A-Z]{3,4})\\s+irun)(?<args>.*?)(\\s+(User|The))", 32)));
   private static final Map e = ImmutableMap.of(Pattern.compile("\t\t+.*[\n\r]+"), "");

   public LogSimulatorResult parseLogFile(LogSimulatorResult var1, Path var2, BuildConfigParser.SimLogConfigParameters var3, IProgressMonitor var4) {
      this.a(var1, var2, var4, d, var3.maxSizeBuffer, var3.fullParsing);
      return var4.isCanceled() ? LogSimulatorResult.a : var1;
   }

   public List getExtractionPatterns() {
      return d;
   }

   public Map getReplacementPatterns() {
      return e;
   }
}
