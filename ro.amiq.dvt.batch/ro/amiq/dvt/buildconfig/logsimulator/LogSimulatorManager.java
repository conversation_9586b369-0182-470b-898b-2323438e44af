package ro.amiq.dvt.buildconfig.logsimulator;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import org.apache.commons.lang.StringEscapeUtils;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IPath;
import org.eclipse.core.runtime.IProgressMonitor;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.buildconfig.BuildConfigManager;
import ro.amiq.dvt.buildconfig.BuildConfigParser;
import ro.amiq.dvt.buildconfig.IBuildConfigParserConstants;
import ro.amiq.dvt.buildconfig.Invocation;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.builders.DVTBuildConsoleRegistry;
import ro.amiq.dvt.startup.core.DVTLogger;

public final class LogSimulatorManager {
   private static final String b = "# The simulator type could not be detected automatically! Please explicit specify it.";
   private static final String c = System.lineSeparator();
   private static final String d = "[USRDEF] ";
   private static final String e = "[PREDEF] ";
   public static final String a = "SimLog-Config - ";

   private LogSimulatorManager() {
   }

   public static LogSimulatorResult a(IProject var0, Invocation var1, BuildConfigParser.SimLogConfigParameters var2) {
      TimeoutProgressMonitor var3 = new TimeoutProgressMonitor(BuildConfigManager.bY(var0), var2.timeoutSeconds);
      LogSimulatorResult var4 = new LogSimulatorResult();
      DVTBuildConsole var5 = DVTBuildConsoleRegistry.a(var0);
      boolean var6 = var2.userSpecifiedPatterns != null;
      SimulatorType var7 = a(var1, var2, var3, var4, var5, var6);
      if (var7 == null) {
         return var4;
      } else {
         try {
            for(Path var8 : var2.simLogFiles) {
               a(var8, var2, var3, var4, var7, var5, var6);
            }

            a((BuildConfigParser.SimLogConfigParameters)var2, (IProgressMonitor)var3, (LogSimulatorResult)var4, (SimulatorType)var7, (DVTBuildConsole)var5);
            a(var0, var1, var2, var4, var7);
         } catch (CancellableCharSequence.CharSequenceCancelledException var10) {
            if (var3.b()) {
               var4.a(LogSimulatorResult.Status.TIMEOUT);
            } else {
               var4.a(LogSimulatorResult.Status.CANCELED);
            }
         }

         return var4;
      }
   }

   private static SimulatorType a(Invocation var0, BuildConfigParser.SimLogConfigParameters var1, IProgressMonitor var2, LogSimulatorResult var3, DVTBuildConsole var4, boolean var5) {
      Object var6 = null;
      long var7 = 0L;
      long var9 = 0L;
      var4.a("SimLog-Config - Detecting simulator type...");
      var7 = System.currentTimeMillis();
      SimulatorType var11;
      if (var5) {
         var11 = SimulatorDetection.INSTANCE.getUserSpecifiedSimulatorType(var0);
      } else {
         var11 = SimulatorDetection.INSTANCE.getSimulator(var0, var1, var2);
         if (var2.isCanceled()) {
            var3.a(LogSimulatorResult.Status.CANCELED);
            return null;
         }
      }

      if (var11 != null && !var3.d()) {
         var9 = System.currentTimeMillis();
         var4.a("SimLog-Config - Done detecting, simulator is " + var11.name() + " [" + (var9 - var7) + "ms]");
         return var11;
      } else {
         var3.c("# The simulator type could not be detected automatically! Please explicit specify it.");
         var3.b();
         return null;
      }
   }

   private static void a(IProject var0, Invocation var1, BuildConfigParser.SimLogConfigParameters var2, LogSimulatorResult var3, SimulatorType var4) {
      if (var3.a() != LogSimulatorResult.Status.CANCELED) {
         var3.b();

         for(int var5 = 0; var5 < var3.e(); ++var5) {
            IPath var6 = var3.b(var5);
            String var7 = a(var6, var4, var0, var1, var2);
            var3.a(var5, var7);
         }

      }
   }

   private static void a(Path var0, BuildConfigParser.SimLogConfigParameters var1, IProgressMonitor var2, LogSimulatorResult var3, SimulatorType var4, DVTBuildConsole var5, boolean var6) {
      long var7 = 0L;
      long var9 = 0L;
      var5.a("SimLog-Config - Scanning logfile:\n\t" + var0);
      var7 = System.currentTimeMillis();
      LogSimulatorParser var11 = var4.getParser();
      if (var6) {
         var11.a(var3, var0, var1, var2);
      } else {
         var11.parseLogFile(var3, var0, var1, var2);
      }

      var9 = System.currentTimeMillis();
      var5.a("SimLog-Config - Using the following patterns:");
      if (var6) {
         var5.a(a("[USRDEF] ", var1.userSpecifiedPatterns));
      } else {
         var5.a(a("[PREDEF] ", var4.getParser().getExtractionPatterns()));
      }

      var5.a("SimLog-Config - Done scanning [" + (var9 - var7) + "ms]");
   }

   private static void a(BuildConfigParser.SimLogConfigParameters var0, IProgressMonitor var1, LogSimulatorResult var2, SimulatorType var3, DVTBuildConsole var4) {
      LinkedHashMap var5 = new LinkedHashMap();
      Map var6 = var0.replaceMap;
      if (var6 != null && !var6.isEmpty()) {
         var5.putAll(var6);
      } else {
         var5.putAll(var3.getParser().getReplacementPatterns());
      }

      long var7 = 0L;
      long var9 = 0L;
      var4.a("SimLog-Config - Processing result using ... :");

      for(Map.Entry var11 : var5.entrySet()) {
         var4.a("\tReplace '" + StringEscapeUtils.escapeJava(((Pattern)var11.getKey()).toString()) + "' with '" + (String)var11.getValue() + "'");
      }

      var7 = System.currentTimeMillis();

      for(int var20 = 0; var20 < var2.e(); ++var20) {
         for(Map.Entry var21 : var5.entrySet()) {
            if (var1.isCanceled()) {
               var2.a(LogSimulatorResult.Status.CANCELED);
               return;
            }

            CancellableCharSequence var14 = new CancellableCharSequence(var2.a(var20), var1);
            StringBuffer var15 = new StringBuffer();
            Matcher var16 = ((Pattern)var21.getKey()).matcher(var14);

            while(var16.find()) {
               String var17 = var16.group();
               if (var17 != null && !var17.isEmpty()) {
                  var16.appendReplacement(var15, (String)var21.getValue());
               }
            }

            var16.appendTail(var15);
            var2.a(var20, var15);
         }
      }

      var9 = System.currentTimeMillis();
      var4.a("SimLog-Config - Done processing [" + (var9 - var7) + "ms]");
   }

   private static String a(String var0, List var1) {
      return var0 != null && var1 != null ? "\t" + (String)var1.stream().map((var1x) -> var0 + var1x.toString()).collect(Collectors.joining(c + "\t")) : "";
   }

   private static String a(IPath var0, SimulatorType var1, IProject var2, Invocation var3, BuildConfigParser.SimLogConfigParameters var4) {
      StringBuilder var5 = new StringBuilder();
      String var6 = var3.getState().fToolCompat == IBuildConfigParserConstants.ToolCompat.DVT ? var1.getToolCompat().toString() : var3.getState().fToolCompat.toString();
      var5.append("+dvt_init+").append(var6).append(c);
      boolean var7 = a(var2, var3);
      if (var7) {
         String var8 = a(var2.getLocation(), var0);
         if (var8 != null) {
            var5.append("+dvt_compilation_root+").append(var8).append(c);
         }
      }

      var5.append(var4.reuseText).append(c);
      return var5.toString();
   }

   private static boolean a(IProject var0, Invocation var1) {
      boolean var2 = false;

      try {
         var2 = Files.isSameFile(var0.getLocation().toFile().toPath(), Paths.get(var1.getState().fCompilationRoot));
      } catch (IOException var4) {
         DVTLogger.INSTANCE.logError((Throwable)var4);
      }

      return var2;
   }

   private static String a(IPath var0, IPath var1) {
      return var1 == null ? var0.toOSString() : var1.makeRelativeTo(var0).toFile().getParent();
   }
}
