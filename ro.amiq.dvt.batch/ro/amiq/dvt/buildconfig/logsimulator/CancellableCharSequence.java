package ro.amiq.dvt.buildconfig.logsimulator;

import org.eclipse.core.runtime.IProgressMonitor;

public class CancellableCharSequence implements CharSequence {
   private CharSequence a;
   private IProgressMonitor b;

   public CancellableCharSequence(CharSequence var1, IProgressMonitor var2) {
      this.a = var1;
      this.b = var2;
   }

   public int length() {
      if (this.b != null && !this.b.isCanceled()) {
         return this.a == null ? -1 : this.a.length();
      } else {
         throw new CharSequenceCancelledException();
      }
   }

   public char charAt(int var1) {
      if (this.b != null && !this.b.isCanceled()) {
         return this.a == null ? '\u0000' : this.a.charAt(var1);
      } else {
         throw new CharSequenceCancelledException();
      }
   }

   public CharSequence subSequence(int var1, int var2) {
      if (this.b != null && !this.b.isCanceled()) {
         return this.a == null ? null : this.a.subSequence(var1, var2);
      } else {
         throw new CharSequenceCancelledException();
      }
   }

   static class CharSequenceCancelledException extends RuntimeException {
      private static final long serialVersionUID = 1L;
   }
}
