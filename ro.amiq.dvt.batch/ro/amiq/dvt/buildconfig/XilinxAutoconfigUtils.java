package ro.amiq.dvt.buildconfig;

import java.io.File;
import org.apache.commons.io.FilenameUtils;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.IProgressMonitor;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.startup.core.DVTLogger;
import ro.amiq.dvt.utils.DVTStringUtil;

public class XilinxAutoconfigUtils {
   public static final String a = "Autoconfig - Xilinx: ";

   public static AutoConfigResult a(IProject var0, String var1, XilinxProjectInfo var2, BuildConfigParser.AutoConfigParameters var3, DVTBuildConsole var4, IProgressMonitor var5) {
      try {
         File[] var6 = var2.d;
         if (var6.length == 0) {
            throw new Exception("No Xilinx " + var2.c.toolName + " project files found!");
         } else {
            File var7 = null;
            if (var2.c == XilinxAutoconfigUtils.XilinxProjectType.ISE_PROJECT) {
               String var8 = (String)BuildConfigManager.a((IProject)var0, (Invocation)var3.invocation, (IBuildConfigParserConstants.Directive)IBuildConfigParserConstants.Directive.ax);
               if (!var8.isEmpty()) {
                  for(File var9 : var6) {
                     if (var8.equals(FilenameUtils.getName(var9.getCanonicalPath()))) {
                        var7 = var9;
                     }
                  }

                  if (var7 == null) {
                     var4.a("File specified using +dvt_autoconfig_ise_xise not found: " + var8 + ".\nConfigure using existing " + var2.c.projectFileSuffix + " files.");
                  }
               }
            }

            if (var7 == null && var6.length > 1) {
               var4.a("Found multiple " + var2.c.projectFileSuffix + " files:\n    " + DVTStringUtil.a((Object[])var6, (String)"\n    "));
            }

            if (var7 == null) {
               var7 = var6[0];
            }

            if (var7 == null) {
               throw new Exception("Null project file!");
            } else {
               var4.a("Using:\n    " + var7.getCanonicalPath());
               if (!var7.canRead()) {
                  throw new Exception("Project file " + var7.getCanonicalPath() + " not readable!");
               } else {
                  XilinxProjectConfigParser var14 = var2.c.getConfigParser(var0.getName(), var7, var1, var3, var4, var5);
                  if (var14 == null) {
                     return AutoConfigResult.c;
                  } else {
                     if (var14 instanceof XilinxVivadoParser) {
                        String var15 = (String)BuildConfigManager.a((IProject)var0, (Invocation)var3.invocation, (IBuildConfigParserConstants.Directive)IBuildConfigParserConstants.Directive.aw);
                        if (!var15.isEmpty()) {
                           ((XilinxVivadoParser)var14).b(var15);
                        }
                     }

                     return var14.f();
                  }
               }
            }
         }
      } catch (Exception var13) {
         DVTLogger.INSTANCE.logError("Autoconfig - Xilinx: ", var13);
         var4.a("Autoconfig - Xilinx: " + var13.getMessage());
         return (new AutoConfigResult(AutoConfigResult.Status.ERROR, AutoConfigResult.AlgorithmKind.XILINX)).a(var13.getMessage());
      }
   }

   public static class XilinxProjectInfo {
      public String[] a;
      public String b;
      public XilinxProjectType c;
      public File[] d;

      public XilinxProjectInfo(File[] var1, XilinxProjectType var2) {
         this.b = FilenameUtils.getBaseName(var1[0].getName());
         this.c = var2;
         this.d = var1;
      }

      public void a(String[] var1) {
         this.a = var1;
      }
   }

   public static enum XilinxProjectType {
      ISE_PROJECT(".xise", "ISE"),
      VIVADO_PROJECT(".xpr", "Vivado");

      public String projectFileSuffix;
      public String toolName;

      private XilinxProjectType(String var3, String var4) {
         this.projectFileSuffix = var3;
         this.toolName = var4;
      }

      public XilinxProjectConfigParser getConfigParser(String var1, File var2, String var3, BuildConfigParser.AutoConfigParameters var4, DVTBuildConsole var5, IProgressMonitor var6) throws Exception {
         if (this.equals(ISE_PROJECT)) {
            return new XilinxISEParser(var2, var3, var4, var5, var6);
         } else {
            return this.equals(VIVADO_PROJECT) ? new XilinxVivadoParser(var2, var3, var4, var5, var6) : null;
         }
      }

      public String getXilinxTool() {
         return this.toolName;
      }
   }
}
