package ro.amiq.dvt.buildconfig;

import java.io.File;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import javax.xml.xpath.XPathConstants;
import org.apache.commons.io.FilenameUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.startup.core.DVTLogger;

public class XilinxBXMLParser extends XilinxConfigParser {
   public static final String a = "//Root/CompositeFile/FileCollection[@Name='SOURCES']/File";
   private Map k = new LinkedHashMap();
   private Set l = new HashSet(Arrays.asList("IP", "VHDL", "Verilog"));

   public XilinxBXMLParser(File var1, String var2, DVTBuildConsole var3, IProgressMonitor var4) throws Exception {
      super(var1, var2, var3, var4);
   }

   protected void a() throws Exception {
      NodeList var1 = (NodeList)this.a("//Root/CompositeFile/FileCollection[@Name='SOURCES']/File", this.e, XPathConstants.NODESET);
      if (var1 != null) {
         for(int var2 = 0; var2 < var1.getLength(); ++var2) {
            try {
               if (this.i.isCanceled()) {
                  return;
               }

               Node var3 = var1.item(var2);
               if (var3 instanceof Element) {
                  Element var4 = (Element)var3;
                  String var5 = var4.getAttribute("Name");
                  File var6 = this.f.resolve(FilenameUtils.separatorsToSystem(var5)).toFile();
                  if (var6.exists()) {
                     String var7 = var4.getAttribute("Type");
                     if (this.l.contains(var7)) {
                        String var8 = (String)this.a("Library/@Name", var4, XPathConstants.STRING);
                        if (var8 == null) {
                           var8 = "";
                        }

                        this.k.put(var6.getCanonicalPath(), var8);
                     }
                  }
               }
            } catch (Exception var9) {
               DVTLogger.INSTANCE.logError("Exception while parsing files: ", var9);
            }
         }

      }
   }

   public Map b() throws Exception {
      this.a();
      return this.k;
   }
}
