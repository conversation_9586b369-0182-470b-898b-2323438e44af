`define SVT_DATA_UTIL_ALLOC_PATTERN_ELEM_QDA(A, B)
`define SVT_DATA_UTIL_ALLOC_PATTERN_ELEM_BITVEC_QDA(A, B)
`define SVT_DATA_UTIL_ALLOC_PATTERN_ELEM_INT(A, B)
`define SVT_DATA_UTIL_ALLOC_PATTERN_ELEM_BIT(A, B)
`define SVT_DATA_UTIL_CLEAN_PATH(A)
`define SVT_DATA_UTIL_GET_PROP_VAL(A)
`define SVT_DATA_UTIL_REPLACE_PATTERN(A, B, C)
`define SVT_DATA_UTIL_SPLIT(A, B, C)
`define SVT_DEBUG_OPTS_IMP_PORT(A,B,C) SVT_DEBUG_OPTS_IMP_PORT
`define SVT_EVENT_DECL(NAME) event NAME;
`define SVT_EVENT_INIT_EVENT(A)
`define SVT_UART_MAX_RULE 0
`define SVT_UVM_FGP_LOCK 
`define SVT_EVENT_WAIT_EVENT_W_DATA(A, B)


