package ro.amiq.dvt.buildconfig;

import com.google.common.collect.ImmutableMap;
import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import javax.xml.xpath.XPathConstants;
import org.apache.commons.io.FilenameUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.startup.core.DVTLogger;

public class XilinxISEParser extends XilinxProjectConfigParser {
   private static final String u = "/project/properties/property[@name = '";
   private static final String v = "']";
   private static final String w = "/project/files/file";
   private static final String x = "/project/autoManagedFiles/file";
   private static final String y = "/project/libraries/library";
   private static final String z = "No source files found in project configuration!";
   private static final String A = "No library definitions found.";
   private static final String B = "Found included ip core file:\n    ";
   private static final char C = '/';
   private static final String D = "work";
   private static final String E = "library";
   private static final String F = "xil_pn:type";
   private static final String G = "xil_pn:name";
   private static final String H = "xil_pn:value";
   private static final String I = "true";
   private static final String J = "Implementation Top Instance Path";
   private Map K = (new ImmutableMap.Builder()).put("Compile CPLD Simulation Library", "CPLD").put("Compile UNISIM (Functional) Simulation Library", "UNISIM").put("Compile SIMPRIM (Timing) Simulation Library", "SIMPRIM").put("Compile uni9000 (Functional) Simulation Library", "UNI9000_VER").put("Compile XilinxCoreLib (CORE Generator) Simulation Library", "XILINXCORELIB").build();
   private Map L = (new ImmutableMap.Builder()).put("Compile CPLD Simulation Library", "CPLD_VER").put("Compile UNISIM (Functional) Simulation Library", "UNISIMS_VER").put("Compile SIMPRIM (Timing) Simulation Library", "SIMPRIMS_VER").put("Compile uni9000 (Functional) Simulation Library", "UNI9000_VER").put("Compile XilinxCoreLib (CORE Generator) Simulation Library", "XILINXCORELIB_VER").build();
   private static final String M = "Simulation Model Target";
   private static final String N = "Language";
   private static final String O = "Verilog";
   private static final String P = "VHDL";
   private static final String Q = "All";
   private static final String R = "VHDL Source Analysis Standard";
   private static final String S = "Verilog Standard";
   private static final Object T = null;
   private Map U = ImmutableMap.of("VHDL-200X", "VHDL_2008", "VHDL-93", "VHDL_93", "Verilog 2001", "Verilog_2001");
   private Map V;
   private Set W;
   private Set X;
   private Invocation Y;

   public XilinxISEParser(File var1, String var2, BuildConfigParser.AutoConfigParameters var3, DVTBuildConsole var4, IProgressMonitor var5) throws Exception {
      super(var1, var2, var3, var4, var5);
      this.Y = var3 == null ? null : var3.invocation;
      this.V = new LinkedHashMap();
      this.W = new HashSet();
      this.X = new HashSet();
   }

   public void b() throws Exception {
      Object var1 = this.a("/project/properties/property[@name = 'Implementation Top Instance Path']", this.e, XPathConstants.NODE);
      if (var1 instanceof Element) {
         String var2 = ((Element)var1).getAttribute("xil_pn:value");
         int var3 = var2.lastIndexOf(47) + 1;
         if (var3 > 0) {
            String var4 = var2.substring(var3);
            this.s = "-top " + var4 + h + this.s;
         }
      }

      for(Map.Entry var7 : this.K.entrySet()) {
         String var9 = (String)var7.getKey();
         Object var5 = this.a("/project/properties/property[@name = '" + var9 + "']", this.e, XPathConstants.NODE);
         if (var5 instanceof Element) {
            String var6 = ((Element)var5).getAttribute("xil_pn:value");
            if ("true".equals(var6)) {
               if (this.q) {
                  this.W.add((String)var7.getValue());
               } else if (this.r) {
                  this.W.add((String)this.L.get(var9));
               }
            }
         }
      }

   }

   public void c() throws Exception {
      this.i();
      this.h();
      this.g();
   }

   private void g() throws Exception {
      try {
         Object var1 = this.a("/project/autoManagedFiles/file", this.e, XPathConstants.NODESET);
         if (!(var1 instanceof NodeList)) {
            return;
         }

         NodeList var2 = (NodeList)var1;

         for(int var3 = 0; var3 < var2.getLength(); ++var3) {
            if (this.i.isCanceled()) {
               return;
            }

            Element var4 = (Element)var2.item(var3);
            String var5 = var4.getAttribute("xil_pn:name");
            String var6 = var4.getAttribute("xil_pn:type");
            var5 = FilenameUtils.separatorsToSystem(var5);
            if (!var5.isEmpty() && !var6.isEmpty()) {
               ISEFileTypes var7 = XilinxISEParser.ISEFileTypes.a(var6);
               if (var7 != null) {
                  Path var8 = Paths.get(var5);
                  if (!var8.isAbsolute()) {
                     var8 = this.f.resolve(var8);
                  }

                  Path var9 = var8.getParent();
                  if (var9.toFile().exists() && !this.f.equals(var9)) {
                     this.X.add(var9.toFile().getCanonicalPath());
                  }
               }
            }
         }
      } catch (Exception var10) {
         DVTLogger.INSTANCE.logError("Exception while parsing incdirs: ", var10);
      }

   }

   private void h() throws Exception {
      try {
         Object var1 = this.a("/project/files/file", this.e, XPathConstants.NODESET);
         if (!(var1 instanceof NodeList)) {
            this.g.a("No source files found in project configuration!");
            return;
         }

         NodeList var2 = (NodeList)var1;

         for(int var3 = 0; var3 < var2.getLength(); ++var3) {
            try {
               if (this.i.isCanceled()) {
                  return;
               }

               Element var4 = (Element)var2.item(var3);
               String var5 = var4.getAttribute("xil_pn:type");
               String var6 = var4.getAttribute("xil_pn:name");
               var6 = FilenameUtils.separatorsToSystem(var6);
               if (!var6.isEmpty() && !var5.isEmpty()) {
                  ISEFileTypes var7 = XilinxISEParser.ISEFileTypes.a(var5);
                  if (var7 != null) {
                     if (var7 == XilinxISEParser.ISEFileTypes.FILE_IP_INCLUDE) {
                        this.b(var6);
                     } else {
                        String var8 = "";
                        NodeList var9 = var4.getElementsByTagName("library");
                        if (var9 != null && var9.getLength() > 0) {
                           Node var10 = var9.item(0);
                           if (var10 instanceof Element) {
                              var8 = ((Element)var10).getAttribute("xil_pn:name");
                           }
                        }

                        File var14 = Paths.get(var6).toFile();
                        if (!var14.isAbsolute()) {
                           var14 = this.f.resolve(var14.getPath()).toFile();
                        }

                        this.a(this.V, var14.getCanonicalPath(), var8.isEmpty() ? "work" : var8, var7.dvtSyntaxName);
                     }
                  }
               }
            } catch (Exception var11) {
               DVTLogger.INSTANCE.logError("Exception while parsing file: ", var11);
            }
         }
      } catch (Exception var12) {
         DVTLogger.INSTANCE.logError("Exception while parsing project files: ", var12);
      }

   }

   private void b(String var1) {
      try {
         if (this.i.isCanceled()) {
            return;
         }

         File var2 = this.f.resolve(var1).toFile();
         if (!var2.exists()) {
            return;
         }

         this.g.a("Found included ip core file:\n    " + var2.getCanonicalPath());
         XilinxISEParser var3 = new XilinxISEParser(var2, var2.getParentFile().getCanonicalPath(), (BuildConfigParser.AutoConfigParameters)null, this.g, this.i);
         Map var4 = var3.j();

         for(Map.Entry var5 : var4.entrySet()) {
            if (this.i.isCanceled()) {
               return;
            }

            String var7 = (String)var5.getKey();
            XilinxConfigParser.XilinxFilesContainer var8 = (XilinxConfigParser.XilinxFilesContainer)var5.getValue();

            for(Map.Entry var9 : var8.a.entrySet()) {
               String var11 = (String)var9.getKey();
               Collection var12 = (Collection)var9.getValue();
               this.a(this.V, var12, var7, var11);
            }
         }
      } catch (Exception var13) {
         DVTLogger.INSTANCE.logError("Exception while parsing ip core file: " + var1, var13);
      }

   }

   private void i() throws Exception {
      try {
         Object var1 = this.a("/project/libraries/library", this.e, XPathConstants.NODESET);
         if (!(var1 instanceof NodeList)) {
            this.g.a("No library definitions found.");
            return;
         }

         NodeList var2 = (NodeList)var1;

         for(int var3 = 0; var3 < var2.getLength(); ++var3) {
            Element var4 = (Element)var2.item(var3);
            String var5 = var4.getAttribute("xil_pn:name");
            if (!var5.isEmpty() && this.V.get(var5) == null) {
               this.V.put(var5, new XilinxConfigParser.XilinxFilesContainer());
            }
         }
      } catch (Exception var6) {
         DVTLogger.INSTANCE.logError("Exception while parsing project libraries: ", var6);
      }

   }

   protected AutoConfigResult d() {
      if (this.i.isCanceled()) {
         return new AutoConfigResult(AutoConfigResult.Status.CANCELED);
      } else {
         AutoConfigResult var1 = new AutoConfigResult(AutoConfigResult.Status.EMPTY);
         if (!this.t) {
            String var2 = AutoConfig.a(this.W, this.Y);
            if (!var2.isEmpty()) {
               this.s = this.s + h + var2 + h;
            }
         }

         if (!this.s.isEmpty()) {
            var1.a((CharSequence)this.s);
         }

         var1.a(this.a(this.X, Collections.emptyMap(), this.V));
         return var1;
      }
   }

   public String[] e() throws Exception {
      this.q = true;
      this.r = true;
      return new String[]{"ro.amiq.vhdldt.VhdlNature", "ro.amiq.vlogdt.VlogNature"};
   }

   private Map j() throws Exception {
      this.c();
      return this.V;
   }

   protected String a(String var1) {
      return XilinxISEParser.ISEFileTypes.getCompatModeForSyntax(var1);
   }

   static enum ISEFileTypes {
      FILE_VHDL("FILE_VHDL", "vcom", "VHDL_93"),
      FILE_VHDL_2008("VHDL-200X", "vcom", "VHDL_2008"),
      FILE_VERILOG("FILE_VERILOG", "vlog", "Verilog_2001"),
      FILE_IP_INCLUDE("FILE_COREGENISE", "", "");

      private String typeName;
      private String questaCompatMode;
      public String dvtSyntaxName;

      private ISEFileTypes(String var3, String var4, String var5) {
         this.typeName = var3;
         this.questaCompatMode = var4;
         this.dvtSyntaxName = var5;
      }

      private static ISEFileTypes a(String var0) {
         ISEFileTypes[] var4;
         for(ISEFileTypes var1 : var4 = values()) {
            if (var1.typeName.equals(var0)) {
               return var1;
            }
         }

         return null;
      }

      public static String getCompatModeForSyntax(String var0) {
         if (var0 != null && !var0.isEmpty()) {
            ISEFileTypes[] var4;
            for(ISEFileTypes var1 : var4 = values()) {
               if (var1.dvtSyntaxName.equals(var0)) {
                  return "+questa." + var1.questaCompatMode + " ";
               }
            }

            return " ";
         } else {
            return " ";
         }
      }
   }
}
