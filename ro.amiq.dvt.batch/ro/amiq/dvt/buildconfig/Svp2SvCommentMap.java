package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.PatternSyntaxException;
import org.eclipse.core.filesystem.IFileInfo;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.builders.DVTAutoLinkManager;
import ro.amiq.dvt.builders.DVTBuildConsoleRegistry;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.utils.parser.Comment;

public class Svp2SvCommentMap implements Serializable {
   private static final long serialVersionUID = 1L;
   private static final String a = "PFILE";
   private static final String b = "(?<PFILE>";
   private Map fSvp2SvMap = new HashMap();
   private Set fSvSet = new HashSet();
   private Map fPatterns = new HashMap();
   private boolean fDebug;
   private transient IProject c;

   Svp2SvCommentMap(IProject var1) {
      this.c = var1;
   }

   public String findPFileSubstitute(String var1, ParserPath var2) {
      if (this.fSvSet.contains(var2)) {
         return null;
      } else {
         for(Pattern var3 : this.fPatterns.values()) {
            MatchResult var5 = this.a(var3, var1);
            if (var5.a && var5.b != null) {
               ParserPath var6 = new ParserPath(var5.b);
               if (this.fSvp2SvMap.containsKey(var6)) {
                  return null;
               }

               IFileInfo var7 = DVTAutoLinkManager.getInstance().fetchFileInfo(Path.fromOSString(var6.path));
               if (var7.exists() && !var7.isDirectory()) {
                  LinkedHashSet var8 = new LinkedHashSet();
                  var8.add(var2);
                  this.fSvp2SvMap.put(var6, var8);
                  this.fSvSet.add(var2);
                  return var6.path;
               }
            }
         }

         return null;
      }
   }

   private void a(Comment var1, ParserPath var2, Pattern var3, String var4) {
      String var5 = "*** PVLOG COMMENT MAP ***\nMatched pattern:\n'" + var3.pattern() + "'" + (var4 == null ? "\nHowever the (?<PFILE> capturing group did not match any part of the input sequence!" : "\nMapping PVlog file:\n" + var4 + "\nto:" + "\n" + var2.path) + "\nGo to: " + var2 + " at line " + var1.getStartLine() + "\n=================================== COMMENT ====================================\n" + var1.getComment() + "\n" + "================================================================================\n";
      DVTBuildConsoleRegistry.a(this.c).a(var5);
   }

   public String putWithErrorReporting(String var1) {
      Object var2 = null;

      try {
         var5 = Pattern.compile(var1);
      } catch (PatternSyntaxException var4) {
         return "Pattern '" + var1 + "' is not valid: " + var4.getDescription();
      }

      if (var5 == null) {
         return "Pattern '" + var1 + "' is not valid";
      } else if (!var5.pattern().contains("(?<PFILE>")) {
         return "Pattern '" + var1 + "' doesn't contain a " + "(?<PFILE>" + " group";
      } else {
         this.fPatterns.put(var1, var5);
         return null;
      }
   }

   public boolean hasPatterns() {
      return this.c != null && !this.fPatterns.isEmpty();
   }

   public boolean isEmpty() {
      return this.c == null || this.fSvp2SvMap.isEmpty();
   }

   public Set get(ParserPath var1) {
      return (Set)this.fSvp2SvMap.get(var1);
   }

   public Set keySet() {
      return this.fSvp2SvMap.keySet();
   }

   public void setDebug() {
      this.fDebug = true;
   }

   public String test(String var1) {
      if (var1.isEmpty()) {
         return null;
      } else {
         StringBuilder var2 = new StringBuilder();

         for(Pattern var3 : this.fPatterns.values()) {
            MatchResult var5 = this.a(var3, var1);
            var2.append("\n");
            if (var5.a) {
               var2.append("Pattern '" + var3.pattern() + "'" + " matched.");
               if (var5.b != null) {
                  var2.append(" PFILE is '" + var5.b + "'.");
               } else {
                  var2.append(" However the (?<PFILE> capturing group did not match any part of the input sequence!");
               }
            } else {
               var2.append("Pattern '" + var3.pattern() + "'" + " did not match.");
            }
         }

         if (var2.toString().isEmpty()) {
            return null;
         } else {
            return var2.toString();
         }
      }
   }

   private MatchResult a(Pattern var1, String var2) {
      Matcher var3 = var1.matcher(var2);
      String var4 = null;
      boolean var5 = var3.find();
      if (var5) {
         var4 = var3.group("PFILE");
      }

      return new MatchResult(var5, var4);
   }

   public String toString() {
      return "no mapping";
   }

   public void setProject(IProject var1) {
      this.c = var1;
   }

   private static class MatchResult {
      boolean a;
      String b;

      public MatchResult(boolean var1, String var2) {
         this.a = var1;
         this.b = var2;
      }
   }
}
