package ro.amiq.dvt.buildconfig;

import java.util.Timer;
import java.util.TimerTask;

class TimeoutTimer extends Timer {
   private TimeoutTimerTask a;
   private boolean b;
   private long c;
   private long d;
   private long e;

   public void a(long var1) {
      this.a = new TimeoutTimerTask();
      this.b = false;
      this.c = var1;
      this.d = System.currentTimeMillis();
      this.schedule(this.a, var1);
   }

   public long a() {
      this.a.cancel();
      this.a = null;
      this.e = System.currentTimeMillis();
      long var1 = this.e - this.d;
      return var1 >= this.c ? 0L : (this.c - var1) / 1000L * 1000L;
   }

   public boolean b() {
      if (this.a != null) {
         this.b = this.a.a();
      }

      return this.b;
   }

   static class TimeoutTimerTask extends TimerTask {
      private boolean a;

      public void run() {
         this.a = true;
      }

      private boolean a() {
         return this.a;
      }
   }
}
