package ro.amiq.dvt.buildconfig;

import antlr.Token;
import java.util.Arrays;
import java.util.HashSet;

class XLRMHandler implements IVarArgsHandler {
   private static final HashSet a = new HashSet(Arrays.asList("uniq_prior_final", "alt_retain", "gd_pulseprop", "gd_pulsewarn", "coerce-nettype", "module_xmr", "floating_pnt_constraint"));

   public boolean a(IBuildConfigParserConstants.ToolCompat var1) {
      return IBuildConfigParserConstants.ey.contains(var1);
   }

   public boolean a(String var1) {
      return a.contains(var1);
   }

   public VarArgsHandlerStatus a(String var1, BuildConfigParser var2) {
      Object var3 = null;
      if (!this.a(var2.f())) {
         return new VarArgsHandlerStatus(var1, false);
      } else {
         Token var4 = var2.b();
         if (var4 == null) {
            return null;
         } else {
            return !this.a(var4.getText()) ? new VarArgsHandlerStatus(var4.getText(), true) : new VarArgsHandlerStatus(var4.getText(), false);
         }
      }
   }
}
