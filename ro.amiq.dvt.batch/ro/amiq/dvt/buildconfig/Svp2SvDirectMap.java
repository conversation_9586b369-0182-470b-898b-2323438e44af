package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;
import ro.amiq.dvt.utils.DVTStringUtil;

public class Svp2SvDirectMap implements Serializable {
   private static final long serialVersionUID = 1L;
   private HashMap fSv2SvpMap = new HashMap();
   private HashMap fSvp2SvMap = new HashMap();

   public String putWithErrorReporting(String var1, String var2) {
      if (this.fSv2SvpMap.containsKey(var1)) {
         return var1 + " already mapped as generated by the following PVerilog files: " + DVTStringUtil.a((Iterable)this.fSv2SvpMap.get(var1), ", ");
      } else if (this.fSvp2SvMap.containsKey(var2)) {
         return var2 + " already mapped as PVerilog for generating the following files: " + DVTStringUtil.a((Iterable)this.fSvp2SvMap.get(var2), ", ");
      } else {
         Object var3 = (Set)this.fSv2SvpMap.get(var1);
         if (var3 == null) {
            var3 = new LinkedHashSet();
            this.fSv2SvpMap.put(var1, var3);
         }

         ((Set)var3).add(var2);
         Object var4 = (Set)this.fSvp2SvMap.get(var2);
         if (var4 == null) {
            var4 = new LinkedHashSet();
            this.fSvp2SvMap.put(var2, var4);
         }

         ((Set)var4).add(var1);
         return null;
      }
   }

   public Set getSvsFromFilename(String var1) {
      return this.fSvp2SvMap.containsKey(var1) ? (Set)this.fSvp2SvMap.get(var1) : null;
   }

   public Set getSvpsFromFileAbsolutePath(String var1) {
      return this.fSv2SvpMap.containsKey(var1) ? (Set)this.fSv2SvpMap.get(var1) : null;
   }

   public Set getAllSvps() {
      return this.fSvp2SvMap.keySet();
   }

   public Set getAllSvs() {
      return this.fSv2SvpMap.keySet();
   }

   public String toString() {
      if (this.fSvp2SvMap.isEmpty()) {
         return "No mapping";
      } else {
         StringBuilder var1 = new StringBuilder();
         Iterator var2 = this.fSvp2SvMap.keySet().iterator();

         while(var2.hasNext()) {
            String var3 = (String)var2.next();
            var1.append(var3).append("=");
            var1.append("{");
            var1.append(DVTStringUtil.a((Iterable)(new ArrayList((Collection)this.fSvp2SvMap.get(var3))), (String)", "));
            var1.append("}");
            if (var2.hasNext()) {
               var1.append("\n");
            }
         }

         return var1.toString();
      }
   }

   public boolean isEmpty() {
      return this.fSvp2SvMap.isEmpty();
   }
}
