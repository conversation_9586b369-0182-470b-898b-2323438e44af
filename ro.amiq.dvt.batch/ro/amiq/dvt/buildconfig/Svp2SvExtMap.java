package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Map;
import java.util.NavigableMap;
import java.util.Set;
import java.util.TreeMap;
import ro.amiq.dvt.utils.DVTStringUtil;

public class Svp2SvExtMap implements Serializable {
   private static final long serialVersionUID = 1L;
   public static final transient Comparator STRLEN_COMPARATOR = new ExtMapComparator((ExtMapComparator)null);
   private TreeMap fSv2SvpMap;
   private TreeMap fSvp2SvMap;

   public Svp2SvExtMap() {
      this.fSv2SvpMap = new TreeMap(STRLEN_COMPARATOR);
      this.fSvp2SvMap = new TreeMap(STRLEN_COMPARATOR);
   }

   public String putWithErrorReporting(String var1, String var2) {
      if (this.fSv2SvpMap.containsKey(var2)) {
         return var2 + " already mapped as generated by the following PVerilog files: " + DVTStringUtil.a((Iterable)this.fSv2SvpMap.get(var2), ", ");
      } else if (this.fSvp2SvMap.containsKey(var1)) {
         return var1 + " already mapped as PVerilog for generating the following files: " + DVTStringUtil.a((Iterable)this.fSvp2SvMap.get(var1), ", ");
      } else {
         Object var3 = (Set)this.fSv2SvpMap.get(var1);
         if (var3 == null) {
            var3 = new LinkedHashSet();
            this.fSv2SvpMap.put(var1, var3);
         }

         ((Set)var3).add(var2);
         Object var4 = (Set)this.fSvp2SvMap.get(var2);
         if (var4 == null) {
            var4 = new LinkedHashSet();
            this.fSvp2SvMap.put(var2, var4);
         }

         ((Set)var4).add(var1);
         return null;
      }
   }

   public Map.Entry getSvsFromFilename(String var1) {
      NavigableMap var2 = this.fSvp2SvMap.descendingMap();

      for(Map.Entry var3 : var2.entrySet()) {
         if (var1.endsWith("." + (String)var3.getKey())) {
            return var3;
         }
      }

      return null;
   }

   public Map.Entry getSvpsFromFilename(String var1) {
      NavigableMap var2 = this.fSv2SvpMap.descendingMap();

      for(Map.Entry var3 : var2.entrySet()) {
         if (var1.endsWith("." + (String)var3.getKey())) {
            return var3;
         }
      }

      return null;
   }

   public Set getSvpExtensions() {
      return this.fSvp2SvMap.keySet();
   }

   public String toString() {
      if (this.fSvp2SvMap.isEmpty()) {
         return "No mapping";
      } else {
         StringBuilder var1 = new StringBuilder();
         Iterator var2 = this.fSvp2SvMap.keySet().iterator();

         while(var2.hasNext()) {
            String var3 = (String)var2.next();
            var1.append(var3).append("=");
            var1.append("{");
            var1.append(DVTStringUtil.a((Iterable)(new ArrayList((Collection)this.fSvp2SvMap.get(var3))), (String)", "));
            var1.append("}");
            if (var2.hasNext()) {
               var1.append("\n");
            }
         }

         return var1.toString();
      }
   }

   public boolean isEmpty() {
      return this.fSvp2SvMap.isEmpty();
   }

   public int hashCode() {
      int var1 = 1;
      var1 = 31 * var1 + (this.fSv2SvpMap == null ? 0 : this.fSv2SvpMap.hashCode());
      var1 = 31 * var1 + (this.fSvp2SvMap == null ? 0 : this.fSvp2SvMap.hashCode());
      return var1;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!(var1 instanceof Svp2SvExtMap)) {
         return false;
      } else {
         Svp2SvExtMap var2 = (Svp2SvExtMap)var1;
         if (this.fSv2SvpMap == null) {
            if (var2.fSv2SvpMap != null) {
               return false;
            }
         } else if (!this.fSv2SvpMap.equals(var2.fSv2SvpMap)) {
            return false;
         }

         if (this.fSvp2SvMap == null) {
            if (var2.fSvp2SvMap != null) {
               return false;
            }
         } else if (!this.fSvp2SvMap.equals(var2.fSvp2SvMap)) {
            return false;
         }

         return true;
      }
   }

   private static class ExtMapComparator implements Serializable, Comparator {
      private static final long serialVersionUID = 1L;

      private ExtMapComparator() {
      }

      public int compare(String var1, String var2) {
         if (var1 == null && var2 == null) {
            return 0;
         } else if (var1 == null) {
            return -1;
         } else if (var2 == null) {
            return 1;
         } else {
            return var1.length() == var2.length() ? var1.compareTo(var2) : var1.length() - var2.length();
         }
      }

      // $FF: synthetic method
      ExtMapComparator(ExtMapComparator var1) {
         this();
      }
   }
}
