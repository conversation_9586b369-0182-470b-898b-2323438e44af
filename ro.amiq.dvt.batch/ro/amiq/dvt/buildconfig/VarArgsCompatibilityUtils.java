package ro.amiq.dvt.buildconfig;

import com.google.common.collect.ImmutableMap;
import java.util.Map;

public class VarArgsCompatibilityUtils implements IBuildConfigParserConstants {
   private static final Map a = ImmutableMap.of("-xlrm", new XLRMHandler(), "-cm_pragma", new CMPragmaHandler(), "-precompile", new PrecompilePragmaHandler());

   public static boolean a(String var0) {
      return a.containsKey(var0);
   }

   private static IVarArgsHandler c(String var0) {
      return (IVarArgsHandler)a.get(var0);
   }

   public static VarArgsHandlerStatus a(String var0, BuildConfigParser var1) {
      return a(var0) ? c(var0).a(var0, var1) : new VarArgsHandlerStatus(var0, true);
   }
}
