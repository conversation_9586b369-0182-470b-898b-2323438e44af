package ro.amiq.dvt.buildconfig;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathConstants;
import javax.xml.xpath.XPathExpression;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.apache.commons.io.FilenameUtils;
import org.eclipse.core.runtime.IProgressMonitor;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.startup.core.DVTLogger;

public class XilinxIPParser extends XilinxConfigParser {
   private static final String a = "true";
   private static final String k = "component/fileSets/fileSet[name = '";
   private static final String l = "']/file";
   private static final String m = "fileType/text()";
   private static final String n = "userFileType/text()";
   private static final String o = "name/text()";
   private static final String p = "logicalName/text()";
   private static final String q = "isIncludeFile/text()";
   private static final String r = "xilinx_elaboratesubcores";
   private static final String s = "xilinx_verilogbehavioralsimulation";
   private static final String t = "xilinx_verilogsimulationwrapper";
   private static final String u = "xilinx_vhdlbehavioralsimulation";
   private static final String v = "xilinx_vhdlsimulationwrapper";
   private static final String w = "xilinx_anylanguagebehavioralsimulation";
   private static final String x = "xilinx_anylanguagesimulationwrapper";
   private static final String y = "component/model/views/view[name = 'xilinx_elaboratesubcores']/fileSetRef/localName/text()";
   private static final String z = "component/model/views/view[name = 'xilinx_verilogbehavioralsimulation']/fileSetRef/localName/text()";
   private static final String A = "component/model/views/view[name = 'xilinx_verilogsimulationwrapper']/fileSetRef/localName/text()";
   private static final String B = "component/model/views/view[name = 'xilinx_vhdlbehavioralsimulation']/fileSetRef/localName/text()";
   private static final String C = "component/model/views/view[name = 'xilinx_vhdlsimulationwrapper']/fileSetRef/localName/text()";
   private static final String D = "component/model/views/view[name = 'xilinx_anylanguagebehavioralsimulation']/fileSetRef/localName/text()";
   private static final String E = "component/model/views/view[name = 'xilinx_anylanguagesimulationwrapper']/fileSetRef/localName/text()";
   private File F;
   private String G;
   private Set H;
   private Map I;

   public XilinxIPParser(File var1, String var2, String var3, DVTBuildConsole var4, IProgressMonitor var5) throws Exception {
      super(var1, var2, var4, var5);
      this.G = var3;
      this.F = var1.getParentFile();
      this.H = new HashSet();
      this.I = new LinkedHashMap();
   }

   protected void a() throws Exception {
      this.a("component/model/views/view[name = 'xilinx_verilogbehavioralsimulation']/fileSetRef/localName/text()");
      this.a("component/model/views/view[name = 'xilinx_verilogsimulationwrapper']/fileSetRef/localName/text()");
      this.a("component/model/views/view[name = 'xilinx_vhdlbehavioralsimulation']/fileSetRef/localName/text()");
      this.a("component/model/views/view[name = 'xilinx_vhdlsimulationwrapper']/fileSetRef/localName/text()");
      this.a("component/model/views/view[name = 'xilinx_anylanguagebehavioralsimulation']/fileSetRef/localName/text()");
      this.a("component/model/views/view[name = 'xilinx_anylanguagesimulationwrapper']/fileSetRef/localName/text()");
      this.a("component/model/views/view[name = 'xilinx_elaboratesubcores']/fileSetRef/localName/text()");
   }

   private void a(String var1) throws Exception, XPathExpressionException, IOException {
      List var2 = this.b(var1);
      if (var2 != null) {
         for(String var3 : var2) {
            try {
               if (this.i.isCanceled()) {
                  return;
               }

               NodeList var5 = (NodeList)this.a("component/fileSets/fileSet[name = '" + var3 + "']/file", this.e, XPathConstants.NODESET);
               if (var5 != null && var5.getLength() != 0) {
                  for(int var6 = 0; var6 < var5.getLength(); ++var6) {
                     if (this.i.isCanceled()) {
                        return;
                     }

                     Node var7 = var5.item(var6);
                     Node var8 = (Node)this.a("name/text()", var7, XPathConstants.NODE);
                     if (var8.getNodeType() == 3) {
                        String var9 = var8.getNodeValue();
                        if (var9 != null && !var9.isEmpty()) {
                           Node var10 = (Node)this.a("fileType/text()", var7, XPathConstants.NODE);
                           if (var10 == null) {
                              var10 = (Node)this.a("userFileType/text()", var7, XPathConstants.NODE);
                           }

                           if (var10 != null && var10.getNodeType() == 3) {
                              String var11 = var10.getNodeValue();
                              XilinxVivadoParser.VivadoFileTypes var12 = XilinxVivadoParser.VivadoFileTypes.getFileTypeFromIPFileType(var11);
                              if (var12 != null) {
                                 var9 = FilenameUtils.separatorsToSystem(var9);
                                 Path var13 = this.F.toPath().resolve(var9);
                                 if (var13.toFile().exists()) {
                                    if (var12.equals(XilinxVivadoParser.VivadoFileTypes.IP_INCLUDE)) {
                                       XilinxIPParser var14 = new XilinxIPParser(var13.toFile(), var13.getParent().toAbsolutePath().toString(), this.G, this.g, this.i);
                                       this.a(var14.b());
                                    } else {
                                       Node var20 = (Node)this.a("isIncludeFile/text()", var7, XPathConstants.NODE);
                                       if (var20 != null && var20.getNodeType() == 3 && "true".equals(var20.getNodeValue())) {
                                          this.H.add(var13.getParent().toFile().getCanonicalPath());
                                       } else {
                                          String var15 = this.G;
                                          Node var16 = (Node)this.a("logicalName/text()", var7, XPathConstants.NODE);
                                          if (var16 != null && var16.getNodeType() == 3) {
                                             String var17 = var16.getNodeValue();
                                             if (var17 != null && !var17.isEmpty()) {
                                                var15 = var17;
                                             }
                                          }

                                          this.a(this.I, var13.toFile().getCanonicalPath(), var15, var12.dvtSyntaxName);
                                       }
                                    }
                                 }
                              }
                           }
                        }
                     }
                  }
               }
            } catch (Exception var18) {
               DVTLogger.INSTANCE.logError("Exception while parsing fileset: " + var3, var18);
            }
         }

      }
   }

   public ParseResult b() throws Exception {
      this.a();
      return new ParseResult(this.I, this.H);
   }

   private List b(String var1) throws Exception {
      ArrayList var2 = new ArrayList();
      XPath var3 = XPathFactory.newInstance().newXPath();
      XPathExpression var4 = var3.compile(var1);
      NodeList var5 = (NodeList)var4.evaluate(this.e, XPathConstants.NODESET);

      for(int var6 = 0; var6 < var5.getLength(); ++var6) {
         if (this.i.isCanceled()) {
            return var2;
         }

         Node var7 = var5.item(var6);
         if (var7.getNodeType() == 3) {
            String var8 = var7.getNodeValue();
            if (var8 != null && !var8.isEmpty()) {
               var2.add(var8);
            }
         }
      }

      return var2;
   }

   public void a(ParseResult var1) {
      this.H.addAll(var1.b);

      for(Map.Entry var2 : var1.a.entrySet()) {
         String var4 = (String)var2.getKey();
         XilinxConfigParser.XilinxFilesContainer var5 = (XilinxConfigParser.XilinxFilesContainer)var2.getValue();

         for(Map.Entry var6 : var5.a.entrySet()) {
            this.a(this.I, (Collection)var6.getValue(), var4, (String)var6.getKey());
         }
      }

   }

   static class ParseResult {
      Map a;
      Set b;

      public ParseResult(Map var1, Set var2) {
         this.b = var2;
         this.a = var1;
      }
   }
}
