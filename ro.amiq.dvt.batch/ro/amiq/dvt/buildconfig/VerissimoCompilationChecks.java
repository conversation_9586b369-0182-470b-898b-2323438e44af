package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class VerissimoCompilationChecks implements Serializable, IEnumProvider {
   private static final long serialVersionUID = 1L;
   private Set checksKind = new HashSet();

   public VerissimoCompilationChecks(List var1) {
      if (var1 != null) {
         for(String var2 : var1) {
            this.checksKind.add(VerissimoChecksKind.from(var2));
         }
      }

   }

   public Set getChecksKind() {
      return this.checksKind;
   }

   public Class getEnumClass() {
      return VerissimoChecksKind.class;
   }

   public String toString() {
      if (this.checksKind != null && !this.checksKind.isEmpty()) {
         StringBuilder var1 = new StringBuilder();

         for(VerissimoChecksKind var2 : this.checksKind) {
            var1.append(var2.toString()).append("+");
         }

         var1.deleteCharAt(var1.length());
         return var1.toString();
      } else {
         return "No default value!";
      }
   }

   public int hashCode() {
      int var1 = 1;
      var1 = 31 * var1 + (this.checksKind == null ? 0 : this.checksKind.hashCode());
      return var1;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!(var1 instanceof VerissimoCompilationChecks)) {
         return false;
      } else {
         VerissimoCompilationChecks var2 = (VerissimoCompilationChecks)var1;
         if (this.checksKind == null) {
            if (var2.checksKind != null) {
               return false;
            }
         } else if (!this.checksKind.equals(var2.checksKind)) {
            return false;
         }

         return true;
      }
   }
}
