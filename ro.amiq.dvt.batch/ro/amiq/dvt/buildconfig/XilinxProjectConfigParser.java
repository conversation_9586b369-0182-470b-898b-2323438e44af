package ro.amiq.dvt.buildconfig;

import java.io.File;
import java.util.Collection;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import org.eclipse.core.runtime.IProgressMonitor;
import ro.amiq.dvt.builders.DVTBuildConsole;

public abstract class XilinxProjectConfigParser extends XilinxConfigParser {
   protected static final String a = "+incdir+";
   protected static final String k = "-top ";
   protected static final String l = "+define+";
   protected static final String m = "+dvt_prepend_init";
   protected static final String n = "+dvt_init";
   protected static final String o = "-work ";
   protected static final String p = "+questa.";
   protected boolean q = false;
   protected boolean r = false;
   protected String s = "";
   protected boolean t;
   private static Set u = new HashSet();

   static {
      u.add(XilinxVivadoParser.VivadoFileTypes.SYSTEM_VERILOG_FILE.dvtSyntaxName);
      u.add(XilinxVivadoParser.VivadoFileTypes.VERILOG_FILE.dvtSyntaxName);
      u.add(XilinxVivadoParser.VivadoFileTypes.VERILOG_HEADER_FILE.dvtSyntaxName);
      u.add(XilinxISEParser.ISEFileTypes.FILE_VERILOG.dvtSyntaxName);
   }

   public XilinxProjectConfigParser(File var1, String var2, BuildConfigParser.AutoConfigParameters var3, DVTBuildConsole var4, IProgressMonitor var5) throws Exception {
      super(var1, var2, var4, var5);
      if (var3 != null) {
         if (var3.reuseText != null && !var3.reuseText.trim().isEmpty()) {
            this.s = this.s + "+dvt_init" + var3.reuseText + h;
         }

         this.t = var3.hasPreviousInitXilinxInvocation;
      }

   }

   public AutoConfigResult f() throws Exception {
      this.e();
      this.a();
      return this.d();
   }

   public void a() throws Exception {
      this.b();
      this.c();
   }

   protected AutoConfigResult a(Set var1, Map var2, Map var3) {
      if (this.i.isCanceled()) {
         return new AutoConfigResult(AutoConfigResult.Status.CANCELED);
      } else {
         AutoConfigResult var4 = new AutoConfigResult(AutoConfigResult.Status.OK, AutoConfigResult.AlgorithmKind.XILINX);
         String var5 = "";

         for(String var6 : var1) {
            var5 = var5 + "+incdir+" + var6 + h;
         }

         String var18 = "";

         for(Map.Entry var19 : var2.entrySet()) {
            var18 = var18 + "+define+" + (String)var19.getKey() + "=" + (String)var19.getValue() + h;
         }

         for(Map.Entry var20 : var3.entrySet()) {
            if (this.i.isCanceled()) {
               return new AutoConfigResult(AutoConfigResult.Status.CANCELED);
            }

            String var9 = (String)var20.getKey();
            XilinxConfigParser.XilinxFilesContainer var10 = (XilinxConfigParser.XilinxFilesContainer)var20.getValue();

            for(Map.Entry var11 : var10.a.entrySet()) {
               String var13 = (String)var11.getKey();
               String var14 = "+dvt_init" + this.a(var13) + "-work " + var9 + h;
               var4.a((CharSequence)var14).a((CharSequence)("+dvt_ext_unmap_all\n+dvt_ext_unmapped_syntax+" + var13 + h));
               if (u.contains(var13)) {
                  if (!var18.isEmpty()) {
                     var4.a((CharSequence)(var18 + h));
                  }

                  if (!var5.isEmpty()) {
                     var4.a((CharSequence)(var5 + h));
                  }
               }

               for(String var16 : (Collection)var11.getValue()) {
                  var4.a((CharSequence)(var16 + h));
               }

               var4.a((CharSequence)h);
            }
         }

         return var4;
      }
   }

   public abstract void b() throws Exception;

   public abstract void c() throws Exception;

   protected abstract AutoConfigResult d() throws Exception;

   public abstract String[] e() throws Exception;

   protected abstract String a(String var1);
}
