package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import ro.amiq.dvt.model.preproc.PreprocMappingsUtils;
import ro.amiq.dvt.utils.DVTStringUtil;

public class Svp2SvPatternTagMap implements Serializable {
   private static final long serialVersionUID = 1L;
   private static final char a = '<';
   private static final char b = '>';
   private static final String c = "No pattern found";
   private static final String d = "Unbalanced angled brackets";
   private static final String e = "Unnamed tag";
   private static final String f = "Pattern cannot be used without a constant part";
   private static final String g = "Adjacent tags";
   private static final String h = "No mapping";
   private Map fSv2SvpMap = new LinkedHashMap();
   private Map fSvp2SvMap = new LinkedHashMap();

   public String putWithErrorReporting(String var1, String var2) {
      try {
         TagPattern var3 = new TagPattern(var2);
         TagPattern var4 = new TagPattern(var1);
         this.a(var3, var4);
         this.fSv2SvpMap.put(var4, var3);
         this.fSvp2SvMap.put(var3, var4);
         return null;
      } catch (BaseTagException var5) {
         return var5.getExplanation();
      }
   }

   private void a(TagPattern var1) throws BaseTagException {
      String var2 = var1.toString();

      for(Map.Entry var3 : this.fSv2SvpMap.entrySet()) {
         TagPattern var5 = (TagPattern)var3.getKey();
         TagPattern var6 = (TagPattern)var3.getValue();
         String var7 = var5.toString();
         String var8 = var6.toString();
         if (var5.parseTags(var2) != null) {
            throw new PatternIsContainedException(var2, var7);
         }

         if (var6.parseTags(var2) != null) {
            throw new PatternIsContainedException(var2, var8);
         }

         if (var1.parseTags(var7) != null) {
            throw new PatternContainsException(var2, var7);
         }

         if (var1.parseTags(var8) != null) {
            throw new PatternContainsException(var2, var8);
         }
      }

   }

   private void a(TagPattern var1, TagPattern var2) throws BaseTagException {
      LinkedHashSet var3 = new LinkedHashSet();
      LinkedHashSet var4 = new LinkedHashSet();

      for(BasePart var5 : var1.parts) {
         if (var5 instanceof TagPart) {
            var3.add(var5.getContent());
         }
      }

      for(BasePart var8 : var2.parts) {
         if (var8 instanceof TagPart) {
            var4.add(var8.getContent());
         }
      }

      LinkedHashSet var9 = new LinkedHashSet();

      for(String var11 : var3) {
         if (!var4.contains(var11)) {
            var9.add(var11);
         }
      }

      for(String var12 : var4) {
         if (!var3.contains(var12)) {
            var9.add(var12);
         }
      }

      if (!var9.isEmpty()) {
         throw new UnusedTagsException(var9);
      }
   }

   public String getFirstPairFileName(String var1, PreprocMappingsUtils.PreprocMappingDirection var2) {
      List var3 = this.getPairFileNames(var1, var2, 1);
      return var3.isEmpty() ? null : (String)var3.get(0);
   }

   public List getPairFileNames(String var1, PreprocMappingsUtils.PreprocMappingDirection var2, int var3) {
      return PreprocMappingsUtils.PreprocMappingDirection.G_TO_P.equals(var2) ? this.a(var1, this.fSv2SvpMap, var3) : this.a(var1, this.fSvp2SvMap, var3);
   }

   private List a(String var1, Map var2, int var3) {
      LinkedList var4 = new LinkedList();

      for(Map.Entry var5 : var2.entrySet()) {
         if (var3 <= 0) {
            return var4;
         }

         TagPattern var7 = (TagPattern)var5.getKey();
         Map var8 = var7.parseTags(var1);
         if (var8 != null) {
            var4.add(((TagPattern)var5.getValue()).fillTags(var8));
            --var3;
         }
      }

      return var4;
   }

   public String toString() {
      return "No mapping";
   }

   public int hashCode() {
      int var1 = 1;
      var1 = 31 * var1 + (this.fSv2SvpMap == null ? 0 : this.fSv2SvpMap.hashCode());
      var1 = 31 * var1 + (this.fSvp2SvMap == null ? 0 : this.fSvp2SvMap.hashCode());
      return var1;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!(var1 instanceof Svp2SvPatternTagMap)) {
         return false;
      } else {
         Svp2SvPatternTagMap var2 = (Svp2SvPatternTagMap)var1;
         if (this.fSv2SvpMap == null) {
            if (var2.fSv2SvpMap != null) {
               return false;
            }
         } else if (!this.fSv2SvpMap.equals(var2.fSv2SvpMap)) {
            return false;
         }

         if (this.fSvp2SvMap == null) {
            if (var2.fSvp2SvMap != null) {
               return false;
            }
         } else if (!this.fSvp2SvMap.equals(var2.fSvp2SvMap)) {
            return false;
         }

         return true;
      }
   }

   public abstract static class BasePart implements Serializable {
      private static final long serialVersionUID = 1L;
      String content;

      protected BasePart(String var1) {
         this.content = var1;
      }

      public String getContent() {
         return this.content;
      }
   }

   private static class FixedPart extends BasePart {
      public FixedPart(String var1) {
         super(var1);
      }

      public String toString() {
         return this.getContent();
      }

      public boolean equals(Object var1) {
         return var1 != null && var1 instanceof FixedPart ? this.content.equals(((FixedPart)var1).getContent()) : false;
      }

      public int hashCode() {
         return this.toString().hashCode();
      }
   }

   private static class TagPart extends BasePart {
      public TagPart(String var1) {
         super(var1);
      }

      public String toString() {
         return '<' + this.getContent() + '>';
      }

      public boolean equals(Object var1) {
         return var1 != null && var1 instanceof TagPart ? this.content.equals(((TagPart)var1).getContent()) : false;
      }

      public int hashCode() {
         return this.toString().hashCode();
      }
   }

   private static class TagPattern implements Serializable {
      private static final long serialVersionUID = 1L;
      private static final int a = 4;
      private List parts = new ArrayList(4);

      public TagPattern(String var1) throws BaseTagException {
         this.a(var1);
      }

      private void a(String var1) throws BaseTagException {
         if (var1 != null && !var1.isEmpty()) {
            int var2 = -1;
            int var3 = -1;
            int var4 = 0;
            boolean var5 = false;

            while(var4 < var1.length()) {
               if (var1.charAt(var4) == '<') {
                  if (var3 + 1 < var4) {
                     this.parts.add(new FixedPart(var1.substring(var3 + 1, var4)));
                     var5 = true;
                  }

                  var2 = var4 + 1;
                  ++var4;
               } else {
                  if (var1.charAt(var4) == '>') {
                     if (var2 == -1) {
                        throw new UnbalancedBracketsException(var1, var4);
                     }

                     if (var2 == var4) {
                        throw new UnnamedTagException(var1, var4);
                     }

                     int var6 = this.parts.size();
                     if (var6 >= 1 && this.parts.get(var6 - 1) instanceof TagPart) {
                        throw new AdjacentTagsException(var1, var4);
                     }

                     this.parts.add(new TagPart(var1.substring(var2, var4)));
                     var3 = var4;
                     var2 = -1;
                  }

                  ++var4;
               }
            }

            if (var2 != -1) {
               throw new UnbalancedBracketsException(var1, var4);
            } else {
               if (var3 + 1 < var4) {
                  this.parts.add(new FixedPart(var1.substring(var3 + 1, var4)));
                  var5 = true;
               }

               if (!var5) {
                  throw new SingleTagPatternException(var1);
               }
            }
         } else {
            throw new NoPatternException(var1);
         }
      }

      public String toString() {
         StringBuilder var1 = new StringBuilder();

         for(BasePart var2 : this.parts) {
            var1.append(var2.toString());
         }

         return var1.toString();
      }

      public Map parseTags(String var1) {
         HashMap var2 = new HashMap();
         int var3 = 0;
         int var4 = 0;
         TagPart var5 = null;

         for(BasePart var6 : this.parts) {
            if (var6 instanceof TagPart) {
               var3 = var4;
               var5 = (TagPart)var6;
            } else {
               String var8 = var6.getContent();
               int var9 = DVTStringUtil.c(var8, var1, var4);
               if (var9 == -1) {
                  return null;
               }

               if (var5 != null) {
                  String var10 = var1.substring(var3, var9);
                  if (var10.isEmpty()) {
                     return null;
                  }

                  String var11 = var5.getContent();
                  String var12 = (String)var2.get(var11);
                  if (var12 != null) {
                     if (!var10.equals(var12)) {
                        return null;
                     }
                  } else {
                     var2.put(var11, var10);
                  }

                  var5 = null;
               }

               var4 = var9 + var8.length();
            }
         }

         if (var5 != null) {
            String var13 = var1.substring(var3);
            String var14 = var5.getContent();
            String var15 = (String)var2.get(var14);
            if (var15 != null) {
               if (!var13.equals(var15)) {
                  return null;
               }
            } else {
               var2.put(var14, var13);
            }

            var4 = var1.length();
         }

         if (var4 != var1.length()) {
            return null;
         } else {
            return var2;
         }
      }

      public String fillTags(Map var1) {
         StringBuilder var2 = new StringBuilder();

         for(BasePart var3 : this.parts) {
            if (var3 instanceof FixedPart) {
               var2.append(var3.getContent());
            } else {
               String var5 = (String)var1.get(var3.getContent());
               if (var5 == null) {
                  return null;
               }

               var2.append(var5);
            }
         }

         return var2.toString();
      }

      public boolean equals(Object var1) {
         return var1 != null && var1 instanceof TagPattern ? this.parts.equals(((TagPattern)var1).parts) : false;
      }

      public int hashCode() {
         return this.parts.hashCode();
      }
   }

   private abstract static class BaseTagException extends Exception {
      private static final long serialVersionUID = 1L;
      private String extraDetails;

      protected BaseTagException(String var1) {
         this.extraDetails = var1;
      }

      protected BaseTagException() {
      }

      public String getExplanation() {
         String var1 = "";
         if (this.extraDetails != null && !this.extraDetails.isEmpty()) {
            var1 = ": " + this.extraDetails;
         }

         return this.getErrorMessage() + var1;
      }

      protected abstract String getErrorMessage();
   }

   private static class NoPatternException extends BaseTagException {
      private static final long serialVersionUID = 1L;

      public NoPatternException(String var1) {
         super(var1);
      }

      protected String getErrorMessage() {
         return "No pattern found";
      }
   }

   private static class UnbalancedBracketsException extends BaseTagException {
      private static final long serialVersionUID = 1L;

      public UnbalancedBracketsException(String var1, int var2) {
         super(var1.substring(0, Math.min(var2 + 1, var1.length())));
      }

      protected String getErrorMessage() {
         return "Unbalanced angled brackets";
      }
   }

   private static class UnnamedTagException extends BaseTagException {
      private static final long serialVersionUID = 1L;

      public UnnamedTagException(String var1, int var2) {
         super(var1.substring(0, Math.min(var2 + 1, var1.length())));
      }

      protected String getErrorMessage() {
         return "Unnamed tag";
      }
   }

   private static class SingleTagPatternException extends BaseTagException {
      private static final long serialVersionUID = 1L;

      public SingleTagPatternException(String var1) {
         super(var1);
      }

      protected String getErrorMessage() {
         return "Pattern cannot be used without a constant part";
      }
   }

   private static class AdjacentTagsException extends BaseTagException {
      private static final long serialVersionUID = 1L;

      public AdjacentTagsException(String var1, int var2) {
         super(var1.substring(0, Math.min(var2 + 1, var1.length())));
      }

      protected String getErrorMessage() {
         return "Adjacent tags";
      }
   }

   private static class PatternIsContainedException extends BaseTagException {
      private static final long serialVersionUID = 1L;
      private String containedPattern;
      private String containingPattern;

      public PatternIsContainedException(String var1, String var2) {
         this.containedPattern = var1;
         this.containingPattern = var2;
      }

      protected String getErrorMessage() {
         return "Pattern '" + this.containedPattern + "' is overlapped by '" + this.containingPattern + "'";
      }
   }

   private static class PatternContainsException extends BaseTagException {
      private static final long serialVersionUID = 1L;
      private String containingPattern;
      private String containedPattern;

      public PatternContainsException(String var1, String var2) {
         this.containingPattern = var1;
         this.containedPattern = var2;
      }

      protected String getErrorMessage() {
         return "Pattern '" + this.containingPattern + "' overlaps '" + this.containedPattern + "'";
      }
   }

   private static class UnusedTagsException extends BaseTagException {
      private static final long serialVersionUID = 1L;
      private Set unusedTags;

      public UnusedTagsException(Set var1) {
         this.unusedTags = var1;
      }

      protected String getErrorMessage() {
         return "Unused tag" + (this.unusedTags.size() > 1 ? "s" : "") + ": " + DVTStringUtil.a((Iterable)this.unusedTags, (String)", ");
      }
   }
}
