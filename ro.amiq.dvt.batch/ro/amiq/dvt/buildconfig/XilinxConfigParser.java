package ro.amiq.dvt.buildconfig;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.Map;
import javax.xml.namespace.QName;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.xpath.XPath;
import javax.xml.xpath.XPathExpressionException;
import javax.xml.xpath.XPathFactory;
import org.eclipse.core.runtime.IProgressMonitor;
import org.w3c.dom.Document;
import org.xml.sax.ErrorHandler;
import org.xml.sax.SAXException;
import org.xml.sax.SAXParseException;
import ro.amiq.dvt.builders.DVTBuildConsole;
import ro.amiq.dvt.builders.DVTBuildConsoleCommon;
import ro.amiq.dvt.utils.DVTStringUtil;

public abstract class XilinxConfigParser {
   protected static final String b = "+dvt_ext_unmap_all\n+dvt_ext_unmapped_syntax+";
   protected File c;
   protected XPath d;
   protected Document e;
   protected Path f;
   protected DVTBuildConsole g;
   protected static final String h = System.lineSeparator();
   private static final ErrorHandler a = new ErrorHandler() {
      public void warning(SAXParseException var1) throws SAXException {
         throw var1;
      }

      public void fatalError(SAXParseException var1) throws SAXException {
         throw var1;
      }

      public void error(SAXParseException var1) throws SAXException {
         throw var1;
      }
   };
   protected IProgressMonitor i;
   protected String j;

   public XilinxConfigParser(File var1, String var2, DVTBuildConsole var3, IProgressMonitor var4) throws Exception {
      this.c = var1;
      this.g = var3;
      this.i = var4;
      this.f = Paths.get(var2);
      this.j = var1.getCanonicalPath();
      this.d = XPathFactory.newInstance().newXPath();
      this.b();
   }

   private void b() throws Exception {
      DocumentBuilderFactory var1 = DocumentBuilderFactory.newInstance();
      DocumentBuilder var2 = var1.newDocumentBuilder();
      var2.setErrorHandler(a);
      this.e = var2.parse(this.c);
      if (this.e == null) {
         throw new Exception("XML Document could not be created by parser!");
      }
   }

   public Object a(String var1, Object var2, QName var3) throws XPathExpressionException {
      return this.d.compile(var1).evaluate(var2, var3);
   }

   protected void a(Map var1, String var2, String var3, String var4) {
      this.g.a("Adding to library " + var3 + " file: " + var2, DVTBuildConsoleCommon.MessageSink.FILE);
      XilinxFilesContainer var5 = this.a(var1, var3);
      var5.a(var2, var4);
   }

   protected void a(Map var1, Collection var2, String var3, String var4) {
      this.g.a("Adding to library " + var3 + " file: " + DVTStringUtil.a((Iterable)var2, (String)"\n"), DVTBuildConsoleCommon.MessageSink.FILE);
      XilinxFilesContainer var5 = this.a(var1, var3);
      var5.a(var2, var4);
   }

   private XilinxFilesContainer a(Map var1, String var2) {
      XilinxFilesContainer var3 = (XilinxFilesContainer)var1.get(var2);
      if (var3 == null) {
         var3 = new XilinxFilesContainer();
         var1.put(var2, var3);
      }

      return var3;
   }

   protected abstract void a() throws Exception;

   protected static class XilinxFilesContainer {
      Map a = new LinkedHashMap();

      public XilinxFilesContainer() {
      }

      public void a(String var1, String var2) {
         Collection var3 = this.a(var2);
         var3.add(var1);
      }

      public void a(Collection var1, String var2) {
         Collection var3 = this.a(var2);
         var3.addAll(var1);
      }

      private Collection a(String var1) {
         Object var2 = (Collection)this.a.get(var1);
         if (var2 == null) {
            var2 = new LinkedHashSet();
            this.a.put(var1, var2);
         }

         return (Collection)var2;
      }
   }
}
