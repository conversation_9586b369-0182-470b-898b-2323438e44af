package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;

public class Svp2SvPathMap implements Serializable {
   private static final long serialVersionUID = 1L;
   private Map fMap = new LinkedHashMap();

   public String toString() {
      if (this.fMap.isEmpty()) {
         return "No mapping";
      } else {
         StringBuilder var1 = new StringBuilder();
         Iterator var2 = this.fMap.keySet().iterator();

         while(var2.hasNext()) {
            String var3 = (String)var2.next();
            var1.append(var3).append("=");
            var1.append("{");
            this.fMap.get(var3);
            var1.append("}");
            if (var2.hasNext()) {
               var1.append("\n");
            }
         }

         return var1.toString();
      }
   }

   public Set keySet() {
      return this.fMap.keySet();
   }

   public String get(String var1) {
      return (String)this.fMap.get(var1);
   }

   public void put(String var1, String var2) {
      this.fMap.put(var1, var2);
   }

   public boolean isEmpty() {
      return this.fMap.isEmpty();
   }

   public int hashCode() {
      int var1 = 1;
      var1 = 31 * var1 + (this.fMap == null ? 0 : this.fMap.hashCode());
      return var1;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!(var1 instanceof Svp2SvPathMap)) {
         return false;
      } else {
         Svp2SvPathMap var2 = (Svp2SvPathMap)var1;
         if (this.fMap == null) {
            if (var2.fMap != null) {
               return false;
            }
         } else if (!this.fMap.equals(var2.fMap)) {
            return false;
         }

         return true;
      }
   }
}
