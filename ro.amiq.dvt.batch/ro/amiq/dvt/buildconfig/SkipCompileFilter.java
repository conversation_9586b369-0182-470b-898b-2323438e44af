package ro.amiq.dvt.buildconfig;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import org.eclipse.core.resources.IProject;
import org.eclipse.core.runtime.Path;
import ro.amiq.dvt.model.reflection.ParserPath;
import ro.amiq.dvt.utils.DVTStringUtil;
import ro.amiq.dvt.utils.StringMatcher;

public class SkipCompileFilter implements Serializable {
   private static final long serialVersionUID = 1L;
   private transient IProject a;
   private List skipCompiles = new ArrayList();

   public SkipCompileFilter(IProject var1) {
      this.a = var1;
   }

   public void add(String var1, boolean var2, boolean var3) {
      var1 = DVTStringUtil.n(var1);
      if (var1 != null && !var1.isEmpty()) {
         this.skipCompiles.add(new SkipCompile(var1, var2, var3));
      }
   }

   public void add(SkipCompileFilter var1) {
      this.skipCompiles.addAll(var1.skipCompiles);
   }

   public boolean shouldSkip(ParserPath var1, boolean var2) {
      boolean var3 = this.a(var1.path, var2);
      BuildConfigManager.a(this.a, var1, var3);
      return var3;
   }

   private boolean a(String var1, boolean var2) {
      if (this.skipCompiles.isEmpty()) {
         return false;
      } else {
         var1 = DVTStringUtil.n(var1);
         if (!var2 && !var1.endsWith("/")) {
            var1 = var1.concat("/");
         }

         String var3 = Path.fromOSString(var1).lastSegment();
         if (var3 == null || !var3.startsWith("__edt__") && !var3.startsWith("__sln__") && !var3.startsWith("__msdl__") && !var3.startsWith("__pss__") && !var3.startsWith("__vlog__")) {
            boolean var4 = !this.skipCompiles.isEmpty() && ((SkipCompile)this.skipCompiles.get(0)).allExcept;
            boolean var5 = false;
            boolean var6 = false;

            for(SkipCompile var7 : this.skipCompiles) {
               boolean var9 = var7.pattern != null && var7.pattern.matcher(var1).matches();
               var9 = var9 || var7.stringMatcher != null && var7.stringMatcher.match(var1);
               if (var9) {
                  var6 = true;
                  var5 = !var7.allExcept;
               }
            }

            if (var4 && !var6) {
               return true;
            } else {
               return var5;
            }
         } else {
            return false;
         }
      }
   }

   public String toString() {
      StringBuilder var1 = new StringBuilder();

      for(SkipCompile var2 : this.skipCompiles) {
         var1.append("[");
         if (var2.allExcept) {
            var1.append("ALL_EXCEPT:");
         }

         if (var2.pattern != null) {
            var1.append(var2.pattern.pattern());
         } else {
            var1.append(var2.stringMatcher.toString());
         }

         var1.append("]");
      }

      return var1.toString();
   }

   public void setProject(IProject var1) {
      this.a = var1;
   }

   public int hashCode() {
      int var1 = 1;
      var1 = 31 * var1 + (this.skipCompiles == null ? 0 : this.skipCompiles.hashCode());
      return var1;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!(var1 instanceof SkipCompileFilter)) {
         return false;
      } else {
         SkipCompileFilter var2 = (SkipCompileFilter)var1;
         if (this.skipCompiles == null) {
            if (var2.skipCompiles != null) {
               return false;
            }
         } else if (!this.skipCompiles.equals(var2.skipCompiles)) {
            return false;
         }

         return true;
      }
   }

   private static class SkipCompile implements Serializable {
      private static final long serialVersionUID = 1L;
      boolean allExcept;
      Pattern pattern;
      StringMatcher stringMatcher;

      SkipCompile(String var1, boolean var2, boolean var3) {
         if (var2) {
            this.pattern = Pattern.compile(var1);
         } else {
            this.stringMatcher = new StringMatcher(var1, false, false, true);
         }

         this.allExcept = var3;
      }

      public int hashCode() {
         int var1 = 1;
         var1 = 31 * var1 + (this.allExcept ? 1231 : 1237);
         var1 = 31 * var1 + (this.stringMatcher == null ? 0 : this.stringMatcher.hashCode());
         return var1;
      }

      public boolean equals(Object var1) {
         if (this == var1) {
            return true;
         } else if (!(var1 instanceof SkipCompile)) {
            return false;
         } else {
            SkipCompile var2 = (SkipCompile)var1;
            if (this.allExcept != var2.allExcept) {
               return false;
            } else {
               if (this.stringMatcher == null) {
                  if (var2.stringMatcher != null) {
                     return false;
                  }
               } else if (!this.stringMatcher.equals(var2.stringMatcher)) {
                  return false;
               }

               return true;
            }
         }
      }
   }
}
