package ro.amiq.dvt.ls;

import java.util.function.Consumer;
import org.eclipse.lsp4j.MessageType;
import ro.amiq.dvt.DVTSplash;
import ro.amiq.dvt.DVTSplashInfo;
import ro.amiq.dvt.flclient.DFLI;
import ro.amiq.dvt.ls.extendedDataTypes.LicenseCoreState;
import ro.amiq.dvt.ls.extendedDataTypes.LicenseGlobalState;
import ro.amiq.dvt.ls.logger.LSLogger;

public enum LSLicenseManager implements Consumer {
   INSTANCE;

   private DVTSplashInfo.State currentCoreState;
   private DVTSplashInfo.State currentGlobalState;
   private String currentGlobalInfo;

   public void initialize() {
      DVTSplash.a();
      DVTSplashInfo.INSTANCE.addGlobalStateListener((var1, var2) -> {
         if (var1 != this.currentGlobalState) {
            this.getArgumentValue(var1);
         }

         this.a(var1, var2);
         this.currentGlobalState = var1;
         this.currentGlobalInfo = var2;
      });
      DVTSplashInfo.INSTANCE.addCoreStateListener((var1) -> {
         this.currentCoreState = var1;
         this.getArgumentValues(var1);
      });
   }

   public void syncronize() {
      this.a(this.currentGlobalState);
      this.a(this.currentGlobalState, this.currentGlobalInfo);
      this.b(this.currentCoreState);
      DFLI.I.m(this);
      this.getArgumentValue(DFLI.I.l());
   }

   public void disconnect() {
      DFLI.I.n(this);
   }

   public void accept(String var1) {
      this.a(var1);
   }

   private void a(DVTSplashInfo.State var1) {
      if (var1 == DVTSplashInfo.State.LICENSE_NOT_SET) {
         LClient.INSTANCE.showPopupMessage(MessageType.Error, "License variables are not set or there is no DVT license source among them. DVT_LICENSE_FILE, DVTLMD_LICENSE_FILE, LM_LICENSE_FILE must point ot a DVT license source!");
      } else if (var1 == DVTSplashInfo.State.ERROR) {
         LClient.INSTANCE.run((var0) -> var0.licenseCheckoutError());
      } else if (var1 == DVTSplashInfo.State.QUEUED) {
         LClient.INSTANCE.showPopupMessage(MessageType.Warning, "No license available, the license check-out is queued!");
      } else if (var1 == DVTSplashInfo.State.TIMEOUT) {
         LClient.INSTANCE.showPopupMessage(MessageType.Warning, "Connection with the license server is unstable, the license will be checked-in soon!");
      }

   }

   private void a(DVTSplashInfo.State var1, String var2) {
      LClient.INSTANCE.run((var2x) -> var2x.licenseUpdateGlobalState(new LicenseGlobalState(var1.toString(), var2)));
   }

   private void b(DVTSplashInfo.State var1) {
      LClient.INSTANCE.run((var1x) -> var1x.licenseUpdateCoreStatus(new LicenseCoreState(var1.toString())));
   }

   private void a(String var1) {
      LSLogger.INSTANCE.logToLicenseConsole(var1);
   }
}
